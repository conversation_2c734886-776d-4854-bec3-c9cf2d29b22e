const {
  createWalletClient,
  getContract,
  createPublicClient,
  parseAbi,
  http,
} = require("viem");
const { privateKeyToAccount } = require("viem/accounts");
const { abstract } = require("viem/chains");
const { sleep } = require("../helper");

const privateKeys = [
  // "0x7cc098e6cb4d344f362f2651d2068dea1dece6e1f0a59aef45c1b74e8e5e2af6",
  "0x71e1c0478360e247461479d566406ee5da102d3067fd5f91290cc930e76abb9d",
  "0xb908e20ed151217a34feaf3d1f8d1e3a7fe26ecd9a19157941aa8fe6592e0659",
  "0xcfad9c0ddfc40c6a993bf2dadd5984805c9007b290875333d7a88c7eddf37ba6",
];

const accountRecipient = "******************************************";
const tokens = [
  "******************************************",
  "******************************************",
  "******************************************",
  "******************************************",
  "******************************************",
];

const abi = parseAbi([
  "function transfer(address to, uint256 amount) public returns (bool)",
  "function balanceOf(address account) public view returns (uint256)",
]);
const publicCLient = createPublicClient({
  chain: abstract,
  transport: http("https://api.mainnet.abs.xyz"),
});

const transferTo = async () => {
  for (const privateKey of privateKeys) {
    const account = privateKeyToAccount(privateKey);

    const walletClient = createWalletClient({
      account,
      chain: abstract,
      transport: http("https://api.mainnet.abs.xyz"),
    });

    for (const token of tokens) {
      const contract = getContract({
        address: token,
        abi: abi,
        client: { public: publicCLient, wallet: walletClient },
      });

      const balance = await contract.read.balanceOf([account.address]);
      console.log({ balance, token }, "balance");

      await sleep(Math.random(10000, 20000));

      if (balance === 0n) {
        continue;
      }

      try {
        const tx = await contract.write.transfer([accountRecipient, balance]);
        console.log(tx, "tx");
      } catch (err) {
        console.log(
          err,
          `transfer account ${account.address} to ${token} error`
        );
      }
    }
  }
};

transferTo();
