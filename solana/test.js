"use strict";
(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
  [1426],
  {
    34985: (e, t, r) => {
      let i;
      r.r(t),
        r.d(t, {
          Account: () => x,
          AddressLookupTableAccount: () => eM,
          AddressLookupTableInstruction: () => rI,
          AddressLookupTableProgram: () => r_,
          Authorized: () => rR,
          BLOCKHASH_CACHE_TIMEOUT_MS: () => eX,
          BPF_LOADER_DEPRECATED_PROGRAM_ID: () => L,
          BPF_LOADER_PROGRAM_ID: () => eC,
          BpfLoader: () => eR,
          COMPUTE_BUDGET_INSTRUCTION_LAYOUTS: () => rA,
          ComputeBudgetInstruction: () => rv,
          ComputeBudgetProgram: () => rP,
          Connection: () => rk,
          Ed25519Program: () => rB,
          Enum: () => W,
          EpochSchedule: () => eY,
          FeeCalculatorLayout: () => eI,
          Keypair: () => rw,
          LAMPORTS_PER_SOL: () => r3,
          LOOKUP_TABLE_INSTRUCTION_LAYOUTS: () => rS,
          Loader: () => eO,
          Lockup: () => rx,
          MAX_SEED_LENGTH: () => N,
          Message: () => ee,
          MessageAccountKeys: () => j,
          MessageV0: () => et,
          NONCE_ACCOUNT_LENGTH: () => ev,
          NonceAccount: () => eA,
          PACKET_DATA_SIZE: () => K,
          PUBLIC_KEY_LENGTH: () => O,
          PublicKey: () => R,
          SIGNATURE_LENGTH_IN_BYTES: () => Y,
          SOLANA_SCHEMA: () => T,
          STAKE_CONFIG_ID: () => rC,
          STAKE_INSTRUCTION_LAYOUTS: () => rK,
          SYSTEM_INSTRUCTION_LAYOUTS: () => eW,
          SYSVAR_CLOCK_PUBKEY: () => eu,
          SYSVAR_EPOCH_SCHEDULE_PUBKEY: () => el,
          SYSVAR_INSTRUCTIONS_PUBKEY: () => ed,
          SYSVAR_RECENT_BLOCKHASHES_PUBKEY: () => eh,
          SYSVAR_RENT_PUBKEY: () => eg,
          SYSVAR_REWARDS_PUBKEY: () => ep,
          SYSVAR_SLOT_HASHES_PUBKEY: () => em,
          SYSVAR_SLOT_HISTORY_PUBKEY: () => ey,
          SYSVAR_STAKE_HISTORY_PUBKEY: () => eb,
          Secp256k1Program: () => rO,
          SendTransactionError: () => eq,
          SolanaJSONRPCError: () => eH,
          SolanaJSONRPCErrorCode: () => eD,
          StakeAuthorizationLayout: () => rz,
          StakeInstruction: () => rL,
          StakeProgram: () => rY,
          Struct: () => B,
          SystemInstruction: () => eB,
          SystemProgram: () => eT,
          Transaction: () => eo,
          TransactionExpiredBlockheightExceededError: () => q,
          TransactionExpiredNonceInvalidError: () => H,
          TransactionExpiredTimeoutError: () => D,
          TransactionInstruction: () => en,
          TransactionMessage: () => ea,
          TransactionStatus: () => ei,
          VALIDATOR_INFO_KEY: () => rM,
          VERSION_PREFIX_MASK: () => z,
          VOTE_PROGRAM_ID: () => r$,
          ValidatorInfo: () => rF,
          VersionedMessage: () => er,
          VersionedTransaction: () => ec,
          VoteAccount: () => rG,
          VoteAuthorizationLayout: () => rj,
          VoteInit: () => rq,
          VoteInstruction: () => rD,
          VoteProgram: () => rU,
          clusterApiUrl: () => r0,
          sendAndConfirmRawTransaction: () => r1,
          sendAndConfirmTransaction: () => ef,
        });
      var s = r(50887),
        n = r(66755),
        o = r(66732),
        a = r.n(o),
        c = r(22735),
        u = r.n(c),
        l = r(94769),
        d = r(84437),
        h = r(68153),
        g = r(35832),
        p = r(87714),
        m = r(83753),
        y = r.n(m),
        b = r(82366),
        f = r(10752),
        k = r(62889),
        w = r(13458);
      let S = n.ev.utils.randomPrivateKey,
        I = () => {
          let e = n.ev.utils.randomPrivateKey(),
            t = _(e),
            r = new Uint8Array(64);
          return (
            r.set(e),
            r.set(t, 32),
            {
              publicKey: t,
              secretKey: r,
            }
          );
        },
        _ = n.ev.getPublicKey;
      function v(e) {
        try {
          return n.ev.ExtendedPoint.fromHex(e), !0;
        } catch {
          return !1;
        }
      }
      let A = (e, t) => n.ev.sign(e, t.slice(0, 32)),
        P = n.ev.verify,
        E = (e) =>
          s.Buffer.isBuffer(e)
            ? e
            : e instanceof Uint8Array
            ? s.Buffer.from(e.buffer, e.byteOffset, e.byteLength)
            : s.Buffer.from(e);
      class B {
        constructor(e) {
          Object.assign(this, e);
        }
        encode() {
          return s.Buffer.from((0, d.serialize)(T, this));
        }
        static decode(e) {
          return (0, d.deserialize)(T, this, e);
        }
        static decodeUnchecked(e) {
          return (0, d.deserializeUnchecked)(T, this, e);
        }
      }
      class W extends B {
        constructor(e) {
          if ((super(e), (this.enum = ""), 1 !== Object.keys(e).length))
            throw Error("Enum can only take single value");
          Object.keys(e).map((e) => {
            this.enum = e;
          });
        }
      }
      let T = new Map(),
        N = 32,
        O = 32,
        C = 1;
      class R extends B {
        constructor(e) {
          if ((super({}), (this._bn = void 0), void 0 !== e._bn))
            this._bn = e._bn;
          else {
            if ("string" == typeof e) {
              let t = u().decode(e);
              if (t.length != O) throw Error("Invalid public key input");
              this._bn = new (a())(t);
            } else this._bn = new (a())(e);
            if (this._bn.byteLength() > O)
              throw Error("Invalid public key input");
          }
        }
        static unique() {
          let e = new R(C);
          return (C += 1), new R(e.toBuffer());
        }
        equals(e) {
          return this._bn.eq(e._bn);
        }
        toBase58() {
          return u().encode(this.toBytes());
        }
        toJSON() {
          return this.toBase58();
        }
        toBytes() {
          let e = this.toBuffer();
          return new Uint8Array(e.buffer, e.byteOffset, e.byteLength);
        }
        toBuffer() {
          let e = this._bn.toArrayLike(s.Buffer);
          if (e.length === O) return e;
          let t = s.Buffer.alloc(32);
          return e.copy(t, 32 - e.length), t;
        }
        get [Symbol.toStringTag]() {
          return `PublicKey(${this.toString()})`;
        }
        toString() {
          return this.toBase58();
        }
        static async createWithSeed(e, t, r) {
          let i = s.Buffer.concat([
            e.toBuffer(),
            s.Buffer.from(t),
            r.toBuffer(),
          ]);
          return new R((0, l.s)(i));
        }
        static createProgramAddressSync(e, t) {
          let r = s.Buffer.alloc(0);
          e.forEach(function (e) {
            if (e.length > N) throw TypeError("Max seed length exceeded");
            r = s.Buffer.concat([r, E(e)]);
          }),
            (r = s.Buffer.concat([
              r,
              t.toBuffer(),
              s.Buffer.from("ProgramDerivedAddress"),
            ]));
          let i = (0, l.s)(r);
          if (v(i))
            throw Error("Invalid seeds, address must fall off the curve");
          return new R(i);
        }
        static async createProgramAddress(e, t) {
          return this.createProgramAddressSync(e, t);
        }
        static findProgramAddressSync(e, t) {
          let r,
            i = 255;
          for (; 0 != i; ) {
            try {
              let n = e.concat(s.Buffer.from([i]));
              r = this.createProgramAddressSync(n, t);
            } catch (e) {
              if (e instanceof TypeError) throw e;
              i--;
              continue;
            }
            return [r, i];
          }
          throw Error("Unable to find a viable program address nonce");
        }
        static async findProgramAddress(e, t) {
          return this.findProgramAddressSync(e, t);
        }
        static isOnCurve(e) {
          return v(new R(e).toBytes());
        }
      }
      (R.default = new R("********************************")),
        T.set(R, {
          kind: "struct",
          fields: [["_bn", "u256"]],
        });
      class x {
        constructor(e) {
          if (((this._publicKey = void 0), (this._secretKey = void 0), e)) {
            let t = E(e);
            if (64 !== e.length) throw Error("bad secret key size");
            (this._publicKey = t.slice(32, 64)),
              (this._secretKey = t.slice(0, 32));
          } else
            (this._secretKey = E(S())),
              (this._publicKey = E(_(this._secretKey)));
        }
        get publicKey() {
          return new R(this._publicKey);
        }
        get secretKey() {
          return s.Buffer.concat([this._secretKey, this._publicKey], 64);
        }
      }
      let L = new R("BPFLoader********************************11"),
        K = 1232,
        z = 127,
        Y = 64;
      class q extends Error {
        constructor(e) {
          super(`Signature ${e} has expired: block height exceeded.`),
            (this.signature = void 0),
            (this.signature = e);
        }
      }
      Object.defineProperty(q.prototype, "name", {
        value: "TransactionExpiredBlockheightExceededError",
      });
      class D extends Error {
        constructor(e, t) {
          super(
            `Transaction was not confirmed in ${t.toFixed(
              2
            )} seconds. It is unknown if it succeeded or failed. Check signature ${e} using the Solana Explorer or CLI tools.`
          ),
            (this.signature = void 0),
            (this.signature = e);
        }
      }
      Object.defineProperty(D.prototype, "name", {
        value: "TransactionExpiredTimeoutError",
      });
      class H extends Error {
        constructor(e) {
          super(`Signature ${e} has expired: the nonce is no longer valid.`),
            (this.signature = void 0),
            (this.signature = e);
        }
      }
      Object.defineProperty(H.prototype, "name", {
        value: "TransactionExpiredNonceInvalidError",
      });
      class j {
        constructor(e, t) {
          (this.staticAccountKeys = void 0),
            (this.accountKeysFromLookups = void 0),
            (this.staticAccountKeys = e),
            (this.accountKeysFromLookups = t);
        }
        keySegments() {
          let e = [this.staticAccountKeys];
          return (
            this.accountKeysFromLookups &&
              (e.push(this.accountKeysFromLookups.writable),
              e.push(this.accountKeysFromLookups.readonly)),
            e
          );
        }
        get(e) {
          for (let t of this.keySegments())
            if (e < t.length) return t[e];
            else e -= t.length;
        }
        get length() {
          return this.keySegments().flat().length;
        }
        compileInstructions(e) {
          if (this.length > 256)
            throw Error(
              "Account index overflow encountered during compilation"
            );
          let t = new Map();
          this.keySegments()
            .flat()
            .forEach((e, r) => {
              t.set(e.toBase58(), r);
            });
          let r = (e) => {
            let r = t.get(e.toBase58());
            if (void 0 === r)
              throw Error(
                "Encountered an unknown instruction account key during compilation"
              );
            return r;
          };
          return e.map((e) => ({
            programIdIndex: r(e.programId),
            accountKeyIndexes: e.keys.map((e) => r(e.pubkey)),
            data: e.data,
          }));
        }
      }
      let U = (e = "publicKey") => h.av(32, e),
        M = (e = "signature") => h.av(64, e),
        V = (e = "string") => {
          let t = h.w3(
              [
                h.DH("length"),
                h.DH("lengthPadding"),
                h.av(h.cY(h.DH(), -8), "chars"),
              ],
              e
            ),
            r = t.decode.bind(t),
            i = t.encode.bind(t);
          return (
            (t.decode = (e, t) => r(e, t).chars.toString()),
            (t.encode = (e, t, r) =>
              i(
                {
                  chars: s.Buffer.from(e, "utf8"),
                },
                t,
                r
              )),
            (t.alloc = (e) =>
              h.DH().span + h.DH().span + s.Buffer.from(e, "utf8").length),
            t
          );
        };
      function F(e) {
        let t = 0,
          r = 0;
        for (;;) {
          let i = e.shift();
          if (((t |= (127 & i) << (7 * r)), (r += 1), (128 & i) == 0)) break;
        }
        return t;
      }
      function $(e, t) {
        let r = t;
        for (;;) {
          let t = 127 & r;
          if (0 == (r >>= 7)) {
            e.push(t);
            break;
          }
          (t |= 128), e.push(t);
        }
      }
      function J(e, t) {
        if (!e) throw Error(t || "Assertion failed");
      }
      class G {
        constructor(e, t) {
          (this.payer = void 0),
            (this.keyMetaMap = void 0),
            (this.payer = e),
            (this.keyMetaMap = t);
        }
        static compile(e, t) {
          let r = new Map(),
            i = (e) => {
              let t = e.toBase58(),
                i = r.get(t);
              return (
                void 0 === i &&
                  ((i = {
                    isSigner: !1,
                    isWritable: !1,
                    isInvoked: !1,
                  }),
                  r.set(t, i)),
                i
              );
            },
            s = i(t);
          for (let t of ((s.isSigner = !0), (s.isWritable = !0), e))
            for (let e of ((i(t.programId).isInvoked = !0), t.keys)) {
              let t = i(e.pubkey);
              (t.isSigner ||= e.isSigner), (t.isWritable ||= e.isWritable);
            }
          return new G(t, r);
        }
        getMessageComponents() {
          let e = [...this.keyMetaMap.entries()];
          J(e.length <= 256, "Max static account keys length exceeded");
          let t = e.filter(([, e]) => e.isSigner && e.isWritable),
            r = e.filter(([, e]) => e.isSigner && !e.isWritable),
            i = e.filter(([, e]) => !e.isSigner && e.isWritable),
            s = e.filter(([, e]) => !e.isSigner && !e.isWritable),
            n = {
              numRequiredSignatures: t.length + r.length,
              numReadonlySignedAccounts: r.length,
              numReadonlyUnsignedAccounts: s.length,
            };
          {
            J(t.length > 0, "Expected at least one writable signer key");
            let [e] = t[0];
            J(
              e === this.payer.toBase58(),
              "Expected first writable signer key to be the fee payer"
            );
          }
          return [
            n,
            [
              ...t.map(([e]) => new R(e)),
              ...r.map(([e]) => new R(e)),
              ...i.map(([e]) => new R(e)),
              ...s.map(([e]) => new R(e)),
            ],
          ];
        }
        extractTableLookup(e) {
          let [t, r] = this.drainKeysFoundInLookupTable(
              e.state.addresses,
              (e) => !e.isSigner && !e.isInvoked && e.isWritable
            ),
            [i, s] = this.drainKeysFoundInLookupTable(
              e.state.addresses,
              (e) => !e.isSigner && !e.isInvoked && !e.isWritable
            );
          if (0 !== t.length || 0 !== i.length)
            return [
              {
                accountKey: e.key,
                writableIndexes: t,
                readonlyIndexes: i,
              },
              {
                writable: r,
                readonly: s,
              },
            ];
        }
        drainKeysFoundInLookupTable(e, t) {
          let r = [],
            i = [];
          for (let [s, n] of this.keyMetaMap.entries())
            if (t(n)) {
              let t = new R(s),
                n = e.findIndex((e) => e.equals(t));
              n >= 0 &&
                (J(n < 256, "Max lookup table index exceeded"),
                r.push(n),
                i.push(t),
                this.keyMetaMap.delete(s));
            }
          return [r, i];
        }
      }
      let X = "Reached end of buffer unexpectedly";
      function Z(e) {
        if (0 === e.length) throw Error(X);
        return e.shift();
      }
      function Q(e, ...t) {
        let [r] = t;
        if (2 === t.length ? r + (t[1] ?? 0) > e.length : r >= e.length)
          throw Error(X);
        return e.splice(...t);
      }
      class ee {
        constructor(e) {
          (this.header = void 0),
            (this.accountKeys = void 0),
            (this.recentBlockhash = void 0),
            (this.instructions = void 0),
            (this.indexToProgramIds = new Map()),
            (this.header = e.header),
            (this.accountKeys = e.accountKeys.map((e) => new R(e))),
            (this.recentBlockhash = e.recentBlockhash),
            (this.instructions = e.instructions),
            this.instructions.forEach((e) =>
              this.indexToProgramIds.set(
                e.programIdIndex,
                this.accountKeys[e.programIdIndex]
              )
            );
        }
        get version() {
          return "legacy";
        }
        get staticAccountKeys() {
          return this.accountKeys;
        }
        get compiledInstructions() {
          return this.instructions.map((e) => ({
            programIdIndex: e.programIdIndex,
            accountKeyIndexes: e.accounts,
            data: u().decode(e.data),
          }));
        }
        get addressTableLookups() {
          return [];
        }
        getAccountKeys() {
          return new j(this.staticAccountKeys);
        }
        static compile(e) {
          let [t, r] = G.compile(
              e.instructions,
              e.payerKey
            ).getMessageComponents(),
            i = new j(r).compileInstructions(e.instructions).map((e) => ({
              programIdIndex: e.programIdIndex,
              accounts: e.accountKeyIndexes,
              data: u().encode(e.data),
            }));
          return new ee({
            header: t,
            accountKeys: r,
            recentBlockhash: e.recentBlockhash,
            instructions: i,
          });
        }
        isAccountSigner(e) {
          return e < this.header.numRequiredSignatures;
        }
        isAccountWritable(e) {
          let t = this.header.numRequiredSignatures;
          if (!(e >= this.header.numRequiredSignatures))
            return e < t - this.header.numReadonlySignedAccounts;
          {
            let r =
              this.accountKeys.length -
              t -
              this.header.numReadonlyUnsignedAccounts;
            return e - t < r;
          }
        }
        isProgramId(e) {
          return this.indexToProgramIds.has(e);
        }
        programIds() {
          return [...this.indexToProgramIds.values()];
        }
        nonProgramIds() {
          return this.accountKeys.filter((e, t) => !this.isProgramId(t));
        }
        serialize() {
          let e = this.accountKeys.length,
            t = [];
          $(t, e);
          let r = this.instructions.map((e) => {
              let { accounts: t, programIdIndex: r } = e,
                i = Array.from(u().decode(e.data)),
                n = [];
              $(n, t.length);
              let o = [];
              return (
                $(o, i.length),
                {
                  programIdIndex: r,
                  keyIndicesCount: s.Buffer.from(n),
                  keyIndices: t,
                  dataLength: s.Buffer.from(o),
                  data: i,
                }
              );
            }),
            i = [];
          $(i, r.length);
          let n = s.Buffer.alloc(K);
          s.Buffer.from(i).copy(n);
          let o = i.length;
          r.forEach((e) => {
            let t = h
              .w3([
                h.u8("programIdIndex"),
                h.av(e.keyIndicesCount.length, "keyIndicesCount"),
                h.O6(h.u8("keyIndex"), e.keyIndices.length, "keyIndices"),
                h.av(e.dataLength.length, "dataLength"),
                h.O6(h.u8("userdatum"), e.data.length, "data"),
              ])
              .encode(e, n, o);
            o += t;
          }),
            (n = n.slice(0, o));
          let a = h.w3([
              h.av(1, "numRequiredSignatures"),
              h.av(1, "numReadonlySignedAccounts"),
              h.av(1, "numReadonlyUnsignedAccounts"),
              h.av(t.length, "keyCount"),
              h.O6(U("key"), e, "keys"),
              U("recentBlockhash"),
            ]),
            c = {
              numRequiredSignatures: s.Buffer.from([
                this.header.numRequiredSignatures,
              ]),
              numReadonlySignedAccounts: s.Buffer.from([
                this.header.numReadonlySignedAccounts,
              ]),
              numReadonlyUnsignedAccounts: s.Buffer.from([
                this.header.numReadonlyUnsignedAccounts,
              ]),
              keyCount: s.Buffer.from(t),
              keys: this.accountKeys.map((e) => E(e.toBytes())),
              recentBlockhash: u().decode(this.recentBlockhash),
            },
            l = s.Buffer.alloc(2048),
            d = a.encode(c, l);
          return n.copy(l, d), l.slice(0, d + n.length);
        }
        static from(e) {
          let t = [...e],
            r = Z(t);
          if (r !== (r & z))
            throw Error(
              "Versioned messages must be deserialized with VersionedMessage.deserialize()"
            );
          let i = Z(t),
            n = Z(t),
            o = F(t),
            a = [];
          for (let e = 0; e < o; e++) {
            let e = Q(t, 0, O);
            a.push(new R(s.Buffer.from(e)));
          }
          let c = Q(t, 0, O),
            l = F(t),
            d = [];
          for (let e = 0; e < l; e++) {
            let e = Z(t),
              r = F(t),
              i = Q(t, 0, r),
              n = F(t),
              o = Q(t, 0, n),
              a = u().encode(s.Buffer.from(o));
            d.push({
              programIdIndex: e,
              accounts: i,
              data: a,
            });
          }
          return new ee({
            header: {
              numRequiredSignatures: r,
              numReadonlySignedAccounts: i,
              numReadonlyUnsignedAccounts: n,
            },
            recentBlockhash: u().encode(s.Buffer.from(c)),
            accountKeys: a,
            instructions: d,
          });
        }
      }
      class et {
        constructor(e) {
          (this.header = void 0),
            (this.staticAccountKeys = void 0),
            (this.recentBlockhash = void 0),
            (this.compiledInstructions = void 0),
            (this.addressTableLookups = void 0),
            (this.header = e.header),
            (this.staticAccountKeys = e.staticAccountKeys),
            (this.recentBlockhash = e.recentBlockhash),
            (this.compiledInstructions = e.compiledInstructions),
            (this.addressTableLookups = e.addressTableLookups);
        }
        get version() {
          return 0;
        }
        get numAccountKeysFromLookups() {
          let e = 0;
          for (let t of this.addressTableLookups)
            e += t.readonlyIndexes.length + t.writableIndexes.length;
          return e;
        }
        getAccountKeys(e) {
          let t;
          if (e && "accountKeysFromLookups" in e && e.accountKeysFromLookups) {
            if (
              this.numAccountKeysFromLookups !=
              e.accountKeysFromLookups.writable.length +
                e.accountKeysFromLookups.readonly.length
            )
              throw Error(
                "Failed to get account keys because of a mismatch in the number of account keys from lookups"
              );
            t = e.accountKeysFromLookups;
          } else if (
            e &&
            "addressLookupTableAccounts" in e &&
            e.addressLookupTableAccounts
          )
            t = this.resolveAddressTableLookups(e.addressLookupTableAccounts);
          else if (this.addressTableLookups.length > 0)
            throw Error(
              "Failed to get account keys because address table lookups were not resolved"
            );
          return new j(this.staticAccountKeys, t);
        }
        isAccountSigner(e) {
          return e < this.header.numRequiredSignatures;
        }
        isAccountWritable(e) {
          let t = this.header.numRequiredSignatures,
            r = this.staticAccountKeys.length;
          if (e >= r)
            return (
              e - r <
              this.addressTableLookups.reduce(
                (e, t) => e + t.writableIndexes.length,
                0
              )
            );
          if (!(e >= this.header.numRequiredSignatures))
            return e < t - this.header.numReadonlySignedAccounts;
          {
            let i = r - t - this.header.numReadonlyUnsignedAccounts;
            return e - t < i;
          }
        }
        resolveAddressTableLookups(e) {
          let t = {
            writable: [],
            readonly: [],
          };
          for (let r of this.addressTableLookups) {
            let i = e.find((e) => e.key.equals(r.accountKey));
            if (!i)
              throw Error(
                `Failed to find address lookup table account for table key ${r.accountKey.toBase58()}`
              );
            for (let e of r.writableIndexes)
              if (e < i.state.addresses.length)
                t.writable.push(i.state.addresses[e]);
              else
                throw Error(
                  `Failed to find address for index ${e} in address lookup table ${r.accountKey.toBase58()}`
                );
            for (let e of r.readonlyIndexes)
              if (e < i.state.addresses.length)
                t.readonly.push(i.state.addresses[e]);
              else
                throw Error(
                  `Failed to find address for index ${e} in address lookup table ${r.accountKey.toBase58()}`
                );
          }
          return t;
        }
        static compile(e) {
          let t = G.compile(e.instructions, e.payerKey),
            r = [],
            i = {
              writable: [],
              readonly: [],
            };
          for (let s of e.addressLookupTableAccounts || []) {
            let e = t.extractTableLookup(s);
            if (void 0 !== e) {
              let [t, { writable: s, readonly: n }] = e;
              r.push(t), i.writable.push(...s), i.readonly.push(...n);
            }
          }
          let [s, n] = t.getMessageComponents(),
            o = new j(n, i).compileInstructions(e.instructions);
          return new et({
            header: s,
            staticAccountKeys: n,
            recentBlockhash: e.recentBlockhash,
            compiledInstructions: o,
            addressTableLookups: r,
          });
        }
        serialize() {
          let e = [];
          $(e, this.staticAccountKeys.length);
          let t = this.serializeInstructions(),
            r = [];
          $(r, this.compiledInstructions.length);
          let i = this.serializeAddressTableLookups(),
            s = [];
          $(s, this.addressTableLookups.length);
          let n = h.w3([
              h.u8("prefix"),
              h.w3(
                [
                  h.u8("numRequiredSignatures"),
                  h.u8("numReadonlySignedAccounts"),
                  h.u8("numReadonlyUnsignedAccounts"),
                ],
                "header"
              ),
              h.av(e.length, "staticAccountKeysLength"),
              h.O6(U(), this.staticAccountKeys.length, "staticAccountKeys"),
              U("recentBlockhash"),
              h.av(r.length, "instructionsLength"),
              h.av(t.length, "serializedInstructions"),
              h.av(s.length, "addressTableLookupsLength"),
              h.av(i.length, "serializedAddressTableLookups"),
            ]),
            o = new Uint8Array(K),
            a = n.encode(
              {
                prefix: 128,
                header: this.header,
                staticAccountKeysLength: new Uint8Array(e),
                staticAccountKeys: this.staticAccountKeys.map((e) =>
                  e.toBytes()
                ),
                recentBlockhash: u().decode(this.recentBlockhash),
                instructionsLength: new Uint8Array(r),
                serializedInstructions: t,
                addressTableLookupsLength: new Uint8Array(s),
                serializedAddressTableLookups: i,
              },
              o
            );
          return o.slice(0, a);
        }
        serializeInstructions() {
          let e = 0,
            t = new Uint8Array(K);
          for (let r of this.compiledInstructions) {
            let i = [];
            $(i, r.accountKeyIndexes.length);
            let s = [];
            $(s, r.data.length);
            let n = h.w3([
              h.u8("programIdIndex"),
              h.av(i.length, "encodedAccountKeyIndexesLength"),
              h.O6(h.u8(), r.accountKeyIndexes.length, "accountKeyIndexes"),
              h.av(s.length, "encodedDataLength"),
              h.av(r.data.length, "data"),
            ]);
            e += n.encode(
              {
                programIdIndex: r.programIdIndex,
                encodedAccountKeyIndexesLength: new Uint8Array(i),
                accountKeyIndexes: r.accountKeyIndexes,
                encodedDataLength: new Uint8Array(s),
                data: r.data,
              },
              t,
              e
            );
          }
          return t.slice(0, e);
        }
        serializeAddressTableLookups() {
          let e = 0,
            t = new Uint8Array(K);
          for (let r of this.addressTableLookups) {
            let i = [];
            $(i, r.writableIndexes.length);
            let s = [];
            $(s, r.readonlyIndexes.length);
            let n = h.w3([
              U("accountKey"),
              h.av(i.length, "encodedWritableIndexesLength"),
              h.O6(h.u8(), r.writableIndexes.length, "writableIndexes"),
              h.av(s.length, "encodedReadonlyIndexesLength"),
              h.O6(h.u8(), r.readonlyIndexes.length, "readonlyIndexes"),
            ]);
            e += n.encode(
              {
                accountKey: r.accountKey.toBytes(),
                encodedWritableIndexesLength: new Uint8Array(i),
                writableIndexes: r.writableIndexes,
                encodedReadonlyIndexesLength: new Uint8Array(s),
                readonlyIndexes: r.readonlyIndexes,
              },
              t,
              e
            );
          }
          return t.slice(0, e);
        }
        static deserialize(e) {
          let t = [...e],
            r = Z(t),
            i = r & z;
          J(r !== i, "Expected versioned message but received legacy message"),
            J(
              0 === i,
              `Expected versioned message with version 0 but found version ${i}`
            );
          let s = {
              numRequiredSignatures: Z(t),
              numReadonlySignedAccounts: Z(t),
              numReadonlyUnsignedAccounts: Z(t),
            },
            n = [],
            o = F(t);
          for (let e = 0; e < o; e++) n.push(new R(Q(t, 0, O)));
          let a = u().encode(Q(t, 0, O)),
            c = F(t),
            l = [];
          for (let e = 0; e < c; e++) {
            let e = Z(t),
              r = F(t),
              i = Q(t, 0, r),
              s = F(t),
              n = new Uint8Array(Q(t, 0, s));
            l.push({
              programIdIndex: e,
              accountKeyIndexes: i,
              data: n,
            });
          }
          let d = F(t),
            h = [];
          for (let e = 0; e < d; e++) {
            let e = new R(Q(t, 0, O)),
              r = F(t),
              i = Q(t, 0, r),
              s = F(t),
              n = Q(t, 0, s);
            h.push({
              accountKey: e,
              writableIndexes: i,
              readonlyIndexes: n,
            });
          }
          return new et({
            header: s,
            staticAccountKeys: n,
            recentBlockhash: a,
            compiledInstructions: l,
            addressTableLookups: h,
          });
        }
      }
      let er = {
          deserializeMessageVersion(e) {
            let t = e[0],
              r = t & z;
            return r === t ? "legacy" : r;
          },
          deserialize: (e) => {
            let t = er.deserializeMessageVersion(e);
            if ("legacy" === t) return ee.from(e);
            if (0 === t) return et.deserialize(e);
            throw Error(
              `Transaction message version ${t} deserialization is not supported`
            );
          },
        },
        ei = (function (e) {
          return (
            (e[(e.BLOCKHEIGHT_EXCEEDED = 0)] = "BLOCKHEIGHT_EXCEEDED"),
            (e[(e.PROCESSED = 1)] = "PROCESSED"),
            (e[(e.TIMED_OUT = 2)] = "TIMED_OUT"),
            (e[(e.NONCE_INVALID = 3)] = "NONCE_INVALID"),
            e
          );
        })({}),
        es = s.Buffer.alloc(Y).fill(0);
      class en {
        constructor(e) {
          (this.keys = void 0),
            (this.programId = void 0),
            (this.data = s.Buffer.alloc(0)),
            (this.programId = e.programId),
            (this.keys = e.keys),
            e.data && (this.data = e.data);
        }
        toJSON() {
          return {
            keys: this.keys.map(
              ({ pubkey: e, isSigner: t, isWritable: r }) => ({
                pubkey: e.toJSON(),
                isSigner: t,
                isWritable: r,
              })
            ),
            programId: this.programId.toJSON(),
            data: [...this.data],
          };
        }
      }
      class eo {
        get signature() {
          return this.signatures.length > 0
            ? this.signatures[0].signature
            : null;
        }
        constructor(e) {
          if (
            ((this.signatures = []),
            (this.feePayer = void 0),
            (this.instructions = []),
            (this.recentBlockhash = void 0),
            (this.lastValidBlockHeight = void 0),
            (this.nonceInfo = void 0),
            (this.minNonceContextSlot = void 0),
            (this._message = void 0),
            (this._json = void 0),
            !e)
          )
            return;
          if (
            (e.feePayer && (this.feePayer = e.feePayer),
            e.signatures && (this.signatures = e.signatures),
            Object.prototype.hasOwnProperty.call(e, "nonceInfo"))
          ) {
            let { minContextSlot: t, nonceInfo: r } = e;
            (this.minNonceContextSlot = t), (this.nonceInfo = r);
          } else if (
            Object.prototype.hasOwnProperty.call(e, "lastValidBlockHeight")
          ) {
            let { blockhash: t, lastValidBlockHeight: r } = e;
            (this.recentBlockhash = t), (this.lastValidBlockHeight = r);
          } else {
            let { recentBlockhash: t, nonceInfo: r } = e;
            r && (this.nonceInfo = r), (this.recentBlockhash = t);
          }
        }
        toJSON() {
          return {
            recentBlockhash: this.recentBlockhash || null,
            feePayer: this.feePayer ? this.feePayer.toJSON() : null,
            nonceInfo: this.nonceInfo
              ? {
                  nonce: this.nonceInfo.nonce,
                  nonceInstruction: this.nonceInfo.nonceInstruction.toJSON(),
                }
              : null,
            instructions: this.instructions.map((e) => e.toJSON()),
            signers: this.signatures.map(({ publicKey: e }) => e.toJSON()),
          };
        }
        add(...e) {
          if (0 === e.length) throw Error("No instructions");
          return (
            e.forEach((e) => {
              "instructions" in e
                ? (this.instructions = this.instructions.concat(e.instructions))
                : "data" in e && "programId" in e && "keys" in e
                ? this.instructions.push(e)
                : this.instructions.push(new en(e));
            }),
            this
          );
        }
        compileMessage() {
          let e, t, r;
          if (
            this._message &&
            JSON.stringify(this.toJSON()) === JSON.stringify(this._json)
          )
            return this._message;
          if (
            (this.nonceInfo
              ? ((e = this.nonceInfo.nonce),
                (t =
                  this.instructions[0] != this.nonceInfo.nonceInstruction
                    ? [this.nonceInfo.nonceInstruction, ...this.instructions]
                    : this.instructions))
              : ((e = this.recentBlockhash), (t = this.instructions)),
            !e)
          )
            throw Error("Transaction recentBlockhash required");
          if (
            (t.length < 1 && console.warn("No instructions provided"),
            this.feePayer)
          )
            r = this.feePayer;
          else if (this.signatures.length > 0 && this.signatures[0].publicKey)
            r = this.signatures[0].publicKey;
          else throw Error("Transaction fee payer required");
          for (let e = 0; e < t.length; e++)
            if (void 0 === t[e].programId)
              throw Error(
                `Transaction instruction index ${e} has undefined program id`
              );
          let i = [],
            s = [];
          t.forEach((e) => {
            e.keys.forEach((e) => {
              s.push({
                ...e,
              });
            });
            let t = e.programId.toString();
            i.includes(t) || i.push(t);
          }),
            i.forEach((e) => {
              s.push({
                pubkey: new R(e),
                isSigner: !1,
                isWritable: !1,
              });
            });
          let n = [];
          s.forEach((e) => {
            let t = e.pubkey.toString(),
              r = n.findIndex((e) => e.pubkey.toString() === t);
            r > -1
              ? ((n[r].isWritable = n[r].isWritable || e.isWritable),
                (n[r].isSigner = n[r].isSigner || e.isSigner))
              : n.push(e);
          }),
            n.sort(function (e, t) {
              return e.isSigner !== t.isSigner
                ? e.isSigner
                  ? -1
                  : 1
                : e.isWritable !== t.isWritable
                ? e.isWritable
                  ? -1
                  : 1
                : e.pubkey.toBase58().localeCompare(t.pubkey.toBase58(), "en", {
                    localeMatcher: "best fit",
                    usage: "sort",
                    sensitivity: "variant",
                    ignorePunctuation: !1,
                    numeric: !1,
                    caseFirst: "lower",
                  });
            });
          let o = n.findIndex((e) => e.pubkey.equals(r));
          if (o > -1) {
            let [e] = n.splice(o, 1);
            (e.isSigner = !0), (e.isWritable = !0), n.unshift(e);
          } else
            n.unshift({
              pubkey: r,
              isSigner: !0,
              isWritable: !0,
            });
          for (let e of this.signatures) {
            let t = n.findIndex((t) => t.pubkey.equals(e.publicKey));
            if (t > -1)
              n[t].isSigner ||
                ((n[t].isSigner = !0),
                console.warn(
                  "Transaction references a signature that is unnecessary, only the fee payer and instruction signer accounts should sign a transaction. This behavior is deprecated and will throw an error in the next major version release."
                ));
            else throw Error(`unknown signer: ${e.publicKey.toString()}`);
          }
          let a = 0,
            c = 0,
            l = 0,
            d = [],
            h = [];
          n.forEach(({ pubkey: e, isSigner: t, isWritable: r }) => {
            t
              ? (d.push(e.toString()), (a += 1), r || (c += 1))
              : (h.push(e.toString()), r || (l += 1));
          });
          let g = d.concat(h),
            p = t.map((e) => {
              let { data: t, programId: r } = e;
              return {
                programIdIndex: g.indexOf(r.toString()),
                accounts: e.keys.map((e) => g.indexOf(e.pubkey.toString())),
                data: u().encode(t),
              };
            });
          return (
            p.forEach((e) => {
              J(e.programIdIndex >= 0), e.accounts.forEach((e) => J(e >= 0));
            }),
            new ee({
              header: {
                numRequiredSignatures: a,
                numReadonlySignedAccounts: c,
                numReadonlyUnsignedAccounts: l,
              },
              accountKeys: g,
              recentBlockhash: e,
              instructions: p,
            })
          );
        }
        _compile() {
          let e = this.compileMessage(),
            t = e.accountKeys.slice(0, e.header.numRequiredSignatures);
          return this.signatures.length === t.length &&
            this.signatures.every((e, r) => t[r].equals(e.publicKey))
            ? e
            : ((this.signatures = t.map((e) => ({
                signature: null,
                publicKey: e,
              }))),
              e);
        }
        serializeMessage() {
          return this._compile().serialize();
        }
        async getEstimatedFee(e) {
          return (await e.getFeeForMessage(this.compileMessage())).value;
        }
        setSigners(...e) {
          if (0 === e.length) throw Error("No signers");
          let t = new Set();
          this.signatures = e
            .filter((e) => {
              let r = e.toString();
              return !t.has(r) && (t.add(r), !0);
            })
            .map((e) => ({
              signature: null,
              publicKey: e,
            }));
        }
        sign(...e) {
          if (0 === e.length) throw Error("No signers");
          let t = new Set(),
            r = [];
          for (let i of e) {
            let e = i.publicKey.toString();
            t.has(e) || (t.add(e), r.push(i));
          }
          this.signatures = r.map((e) => ({
            signature: null,
            publicKey: e.publicKey,
          }));
          let i = this._compile();
          this._partialSign(i, ...r);
        }
        partialSign(...e) {
          if (0 === e.length) throw Error("No signers");
          let t = new Set(),
            r = [];
          for (let i of e) {
            let e = i.publicKey.toString();
            t.has(e) || (t.add(e), r.push(i));
          }
          let i = this._compile();
          this._partialSign(i, ...r);
        }
        _partialSign(e, ...t) {
          let r = e.serialize();
          t.forEach((e) => {
            let t = A(r, e.secretKey);
            this._addSignature(e.publicKey, E(t));
          });
        }
        addSignature(e, t) {
          this._compile(), this._addSignature(e, t);
        }
        _addSignature(e, t) {
          J(64 === t.length);
          let r = this.signatures.findIndex((t) => e.equals(t.publicKey));
          if (r < 0) throw Error(`unknown signer: ${e.toString()}`);
          this.signatures[r].signature = s.Buffer.from(t);
        }
        verifySignatures(e = !0) {
          return !this._getMessageSignednessErrors(this.serializeMessage(), e);
        }
        _getMessageSignednessErrors(e, t) {
          let r = {};
          for (let { signature: i, publicKey: s } of this.signatures)
            null === i
              ? t && (r.missing ||= []).push(s)
              : P(i, e, s.toBytes()) || (r.invalid ||= []).push(s);
          return r.invalid || r.missing ? r : void 0;
        }
        serialize(e) {
          let { requireAllSignatures: t, verifySignatures: r } = Object.assign(
              {
                requireAllSignatures: !0,
                verifySignatures: !0,
              },
              e
            ),
            i = this.serializeMessage();
          if (r) {
            let e = this._getMessageSignednessErrors(i, t);
            if (e) {
              let t = "Signature verification failed.";
              throw (
                (e.invalid &&
                  (t += `
Invalid signature for public key${
                    1 === e.invalid.length ? "" : "(s)"
                  } [\`${e.invalid.map((e) => e.toBase58()).join("`, `")}\`].`),
                e.missing &&
                  (t += `
Missing signature for public key${
                    1 === e.missing.length ? "" : "(s)"
                  } [\`${e.missing.map((e) => e.toBase58()).join("`, `")}\`].`),
                Error(t))
              );
            }
          }
          return this._serialize(i);
        }
        _serialize(e) {
          let { signatures: t } = this,
            r = [];
          $(r, t.length);
          let i = r.length + 64 * t.length + e.length,
            n = s.Buffer.alloc(i);
          return (
            J(t.length < 256),
            s.Buffer.from(r).copy(n, 0),
            t.forEach(({ signature: e }, t) => {
              null !== e &&
                (J(64 === e.length, "signature has invalid length"),
                s.Buffer.from(e).copy(n, r.length + 64 * t));
            }),
            e.copy(n, r.length + 64 * t.length),
            J(n.length <= K, `Transaction too large: ${n.length} > ${K}`),
            n
          );
        }
        get keys() {
          return (
            J(1 === this.instructions.length),
            this.instructions[0].keys.map((e) => e.pubkey)
          );
        }
        get programId() {
          return (
            J(1 === this.instructions.length), this.instructions[0].programId
          );
        }
        get data() {
          return J(1 === this.instructions.length), this.instructions[0].data;
        }
        static from(e) {
          let t = [...e],
            r = F(t),
            i = [];
          for (let e = 0; e < r; e++) {
            let e = Q(t, 0, Y);
            i.push(u().encode(s.Buffer.from(e)));
          }
          return eo.populate(ee.from(t), i);
        }
        static populate(e, t = []) {
          let r = new eo();
          return (
            (r.recentBlockhash = e.recentBlockhash),
            e.header.numRequiredSignatures > 0 &&
              (r.feePayer = e.accountKeys[0]),
            t.forEach((t, i) => {
              let s = {
                signature: t == u().encode(es) ? null : u().decode(t),
                publicKey: e.accountKeys[i],
              };
              r.signatures.push(s);
            }),
            e.instructions.forEach((t) => {
              let i = t.accounts.map((t) => {
                let i = e.accountKeys[t];
                return {
                  pubkey: i,
                  isSigner:
                    r.signatures.some(
                      (e) => e.publicKey.toString() === i.toString()
                    ) || e.isAccountSigner(t),
                  isWritable: e.isAccountWritable(t),
                };
              });
              r.instructions.push(
                new en({
                  keys: i,
                  programId: e.accountKeys[t.programIdIndex],
                  data: u().decode(t.data),
                })
              );
            }),
            (r._message = e),
            (r._json = r.toJSON()),
            r
          );
        }
      }
      class ea {
        constructor(e) {
          (this.payerKey = void 0),
            (this.instructions = void 0),
            (this.recentBlockhash = void 0),
            (this.payerKey = e.payerKey),
            (this.instructions = e.instructions),
            (this.recentBlockhash = e.recentBlockhash);
        }
        static decompile(e, t) {
          let { header: r, compiledInstructions: i, recentBlockhash: s } = e,
            {
              numRequiredSignatures: n,
              numReadonlySignedAccounts: o,
              numReadonlyUnsignedAccounts: a,
            } = r,
            c = n - o;
          J(c > 0, "Message header is invalid");
          let u = e.staticAccountKeys.length - n - a;
          J(u >= 0, "Message header is invalid");
          let l = e.getAccountKeys(t),
            d = l.get(0);
          if (void 0 === d)
            throw Error(
              "Failed to decompile message because no account keys were found"
            );
          let h = [];
          for (let e of i) {
            let t = [];
            for (let i of e.accountKeyIndexes) {
              let e,
                s = l.get(i);
              if (void 0 === s)
                throw Error(`Failed to find key for account key index ${i}`);
              (e =
                i < n
                  ? i < c
                  : i < l.staticAccountKeys.length
                  ? i - n < u
                  : i - l.staticAccountKeys.length <
                    l.accountKeysFromLookups.writable.length),
                t.push({
                  pubkey: s,
                  isSigner: i < r.numRequiredSignatures,
                  isWritable: e,
                });
            }
            let i = l.get(e.programIdIndex);
            if (void 0 === i)
              throw Error(
                `Failed to find program id for program id index ${e.programIdIndex}`
              );
            h.push(
              new en({
                programId: i,
                data: E(e.data),
                keys: t,
              })
            );
          }
          return new ea({
            payerKey: d,
            instructions: h,
            recentBlockhash: s,
          });
        }
        compileToLegacyMessage() {
          return ee.compile({
            payerKey: this.payerKey,
            recentBlockhash: this.recentBlockhash,
            instructions: this.instructions,
          });
        }
        compileToV0Message(e) {
          return et.compile({
            payerKey: this.payerKey,
            recentBlockhash: this.recentBlockhash,
            instructions: this.instructions,
            addressLookupTableAccounts: e,
          });
        }
      }
      class ec {
        get version() {
          return this.message.version;
        }
        constructor(e, t) {
          if (
            ((this.signatures = void 0), (this.message = void 0), void 0 !== t)
          )
            J(
              t.length === e.header.numRequiredSignatures,
              "Expected signatures length to be equal to the number of required signatures"
            ),
              (this.signatures = t);
          else {
            let t = [];
            for (let r = 0; r < e.header.numRequiredSignatures; r++)
              t.push(new Uint8Array(Y));
            this.signatures = t;
          }
          this.message = e;
        }
        serialize() {
          let e = this.message.serialize(),
            t = [];
          $(t, this.signatures.length);
          let r = h.w3([
              h.av(t.length, "encodedSignaturesLength"),
              h.O6(M(), this.signatures.length, "signatures"),
              h.av(e.length, "serializedMessage"),
            ]),
            i = new Uint8Array(2048),
            s = r.encode(
              {
                encodedSignaturesLength: new Uint8Array(t),
                signatures: this.signatures,
                serializedMessage: e,
              },
              i
            );
          return i.slice(0, s);
        }
        static deserialize(e) {
          let t = [...e],
            r = [],
            i = F(t);
          for (let e = 0; e < i; e++) r.push(new Uint8Array(Q(t, 0, Y)));
          return new ec(er.deserialize(new Uint8Array(t)), r);
        }
        sign(e) {
          let t = this.message.serialize(),
            r = this.message.staticAccountKeys.slice(
              0,
              this.message.header.numRequiredSignatures
            );
          for (let i of e) {
            let e = r.findIndex((e) => e.equals(i.publicKey));
            J(
              e >= 0,
              `Cannot sign with non signer key ${i.publicKey.toBase58()}`
            ),
              (this.signatures[e] = A(t, i.secretKey));
          }
        }
        addSignature(e, t) {
          J(64 === t.byteLength, "Signature must be 64 bytes long");
          let r = this.message.staticAccountKeys
            .slice(0, this.message.header.numRequiredSignatures)
            .findIndex((t) => t.equals(e));
          J(
            r >= 0,
            `Can not add signature; \`${e.toBase58()}\` is not required to sign this transaction`
          ),
            (this.signatures[r] = t);
        }
      }
      let eu = new R("SysvarC1ock********************************"),
        el = new R("SysvarEpochSchedu1e****************11111111"),
        ed = new R("Sysvar1nstructions****************111111111"),
        eh = new R("SysvarRecentB1ockHashes****************1111"),
        eg = new R("SysvarRent********************************1"),
        ep = new R("SysvarRewards****************11111111111111"),
        em = new R("SysvarS1otHashes****************11111111111"),
        ey = new R("SysvarS1otHistory****************1111111111"),
        eb = new R("SysvarStakeHistory****************111111111");
      async function ef(e, t, r, i) {
        let s,
          n = i && {
            skipPreflight: i.skipPreflight,
            preflightCommitment: i.preflightCommitment || i.commitment,
            maxRetries: i.maxRetries,
            minContextSlot: i.minContextSlot,
          },
          o = await e.sendTransaction(t, r, n);
        if (null != t.recentBlockhash && null != t.lastValidBlockHeight)
          s = (
            await e.confirmTransaction(
              {
                abortSignal: i?.abortSignal,
                signature: o,
                blockhash: t.recentBlockhash,
                lastValidBlockHeight: t.lastValidBlockHeight,
              },
              i && i.commitment
            )
          ).value;
        else if (null != t.minNonceContextSlot && null != t.nonceInfo) {
          let { nonceInstruction: r } = t.nonceInfo,
            n = r.keys[0].pubkey;
          s = (
            await e.confirmTransaction(
              {
                abortSignal: i?.abortSignal,
                minContextSlot: t.minNonceContextSlot,
                nonceAccountPubkey: n,
                nonceValue: t.nonceInfo.nonce,
                signature: o,
              },
              i && i.commitment
            )
          ).value;
        } else
          i?.abortSignal != null &&
            console.warn(
              "sendAndConfirmTransaction(): A transaction with a deprecated confirmation strategy was supplied along with an `abortSignal`. Only transactions having `lastValidBlockHeight` or a combination of `nonceInfo` and `minNonceContextSlot` are abortable."
            ),
            (s = (await e.confirmTransaction(o, i && i.commitment)).value);
        if (s.err)
          throw Error(`Transaction ${o} failed (${JSON.stringify(s)})`);
        return o;
      }
      function ek(e) {
        return new Promise((t) => setTimeout(t, e));
      }
      function ew(e, t) {
        let r =
            e.layout.span >= 0
              ? e.layout.span
              : (function e(t, r) {
                  let i = (t) => {
                      if (t.span >= 0) return t.span;
                      if ("function" == typeof t.alloc)
                        return t.alloc(r[t.property]);
                      if ("count" in t && "elementLayout" in t) {
                        let e = r[t.property];
                        if (Array.isArray(e))
                          return e.length * i(t.elementLayout);
                      } else if ("fields" in t)
                        return e(
                          {
                            layout: t,
                          },
                          r[t.property]
                        );
                      return 0;
                    },
                    s = 0;
                  return (
                    t.layout.fields.forEach((e) => {
                      s += i(e);
                    }),
                    s
                  );
                })(e, t),
          i = s.Buffer.alloc(r),
          n = Object.assign(
            {
              instruction: e.index,
            },
            t
          );
        return e.layout.encode(n, i), i;
      }
      function eS(e, t) {
        let r;
        try {
          r = e.layout.decode(t);
        } catch (e) {
          throw Error("invalid instruction; " + e);
        }
        if (r.instruction !== e.index)
          throw Error(
            `invalid instruction; instruction index mismatch ${r.instruction} != ${e.index}`
          );
        return r;
      }
      let eI = h.I0("lamportsPerSignature"),
        e_ = h.w3([
          h.DH("version"),
          h.DH("state"),
          U("authorizedPubkey"),
          U("nonce"),
          h.w3([eI], "feeCalculator"),
        ]),
        ev = e_.span;
      class eA {
        constructor(e) {
          (this.authorizedPubkey = void 0),
            (this.nonce = void 0),
            (this.feeCalculator = void 0),
            (this.authorizedPubkey = e.authorizedPubkey),
            (this.nonce = e.nonce),
            (this.feeCalculator = e.feeCalculator);
        }
        static fromAccountData(e) {
          let t = e_.decode(E(e), 0);
          return new eA({
            authorizedPubkey: new R(t.authorizedPubkey),
            nonce: new R(t.nonce).toString(),
            feeCalculator: t.feeCalculator,
          });
        }
      }
      let eP = (e) => ({
          decode: e.decode.bind(e),
          encode: e.encode.bind(e),
        }),
        eE =
          ((i = 8),
          (e) => {
            let t = (0, h.av)(8, e),
              { encode: r, decode: i } = eP(t);
            return (
              (t.decode = (e, t) => {
                let r = i(e, t);
                return (0, g.k5)(s.Buffer.from(r));
              }),
              (t.encode = (e, t, i) => r((0, g.Bq)(e, 8), t, i)),
              t
            );
          });
      class eB {
        constructor() {}
        static decodeInstructionType(e) {
          let t;
          this.checkProgramId(e.programId);
          let r = h.DH("instruction").decode(e.data);
          for (let [e, i] of Object.entries(eW))
            if (i.index == r) {
              t = e;
              break;
            }
          if (!t)
            throw Error("Instruction type incorrect; not a SystemInstruction");
          return t;
        }
        static decodeCreateAccount(e) {
          this.checkProgramId(e.programId), this.checkKeyLength(e.keys, 2);
          let { lamports: t, space: r, programId: i } = eS(eW.Create, e.data);
          return {
            fromPubkey: e.keys[0].pubkey,
            newAccountPubkey: e.keys[1].pubkey,
            lamports: t,
            space: r,
            programId: new R(i),
          };
        }
        static decodeTransfer(e) {
          this.checkProgramId(e.programId), this.checkKeyLength(e.keys, 2);
          let { lamports: t } = eS(eW.Transfer, e.data);
          return {
            fromPubkey: e.keys[0].pubkey,
            toPubkey: e.keys[1].pubkey,
            lamports: t,
          };
        }
        static decodeTransferWithSeed(e) {
          this.checkProgramId(e.programId), this.checkKeyLength(e.keys, 3);
          let {
            lamports: t,
            seed: r,
            programId: i,
          } = eS(eW.TransferWithSeed, e.data);
          return {
            fromPubkey: e.keys[0].pubkey,
            basePubkey: e.keys[1].pubkey,
            toPubkey: e.keys[2].pubkey,
            lamports: t,
            seed: r,
            programId: new R(i),
          };
        }
        static decodeAllocate(e) {
          this.checkProgramId(e.programId), this.checkKeyLength(e.keys, 1);
          let { space: t } = eS(eW.Allocate, e.data);
          return {
            accountPubkey: e.keys[0].pubkey,
            space: t,
          };
        }
        static decodeAllocateWithSeed(e) {
          this.checkProgramId(e.programId), this.checkKeyLength(e.keys, 1);
          let {
            base: t,
            seed: r,
            space: i,
            programId: s,
          } = eS(eW.AllocateWithSeed, e.data);
          return {
            accountPubkey: e.keys[0].pubkey,
            basePubkey: new R(t),
            seed: r,
            space: i,
            programId: new R(s),
          };
        }
        static decodeAssign(e) {
          this.checkProgramId(e.programId), this.checkKeyLength(e.keys, 1);
          let { programId: t } = eS(eW.Assign, e.data);
          return {
            accountPubkey: e.keys[0].pubkey,
            programId: new R(t),
          };
        }
        static decodeAssignWithSeed(e) {
          this.checkProgramId(e.programId), this.checkKeyLength(e.keys, 1);
          let {
            base: t,
            seed: r,
            programId: i,
          } = eS(eW.AssignWithSeed, e.data);
          return {
            accountPubkey: e.keys[0].pubkey,
            basePubkey: new R(t),
            seed: r,
            programId: new R(i),
          };
        }
        static decodeCreateWithSeed(e) {
          this.checkProgramId(e.programId), this.checkKeyLength(e.keys, 2);
          let {
            base: t,
            seed: r,
            lamports: i,
            space: s,
            programId: n,
          } = eS(eW.CreateWithSeed, e.data);
          return {
            fromPubkey: e.keys[0].pubkey,
            newAccountPubkey: e.keys[1].pubkey,
            basePubkey: new R(t),
            seed: r,
            lamports: i,
            space: s,
            programId: new R(n),
          };
        }
        static decodeNonceInitialize(e) {
          this.checkProgramId(e.programId), this.checkKeyLength(e.keys, 3);
          let { authorized: t } = eS(eW.InitializeNonceAccount, e.data);
          return {
            noncePubkey: e.keys[0].pubkey,
            authorizedPubkey: new R(t),
          };
        }
        static decodeNonceAdvance(e) {
          return (
            this.checkProgramId(e.programId),
            this.checkKeyLength(e.keys, 3),
            eS(eW.AdvanceNonceAccount, e.data),
            {
              noncePubkey: e.keys[0].pubkey,
              authorizedPubkey: e.keys[2].pubkey,
            }
          );
        }
        static decodeNonceWithdraw(e) {
          this.checkProgramId(e.programId), this.checkKeyLength(e.keys, 5);
          let { lamports: t } = eS(eW.WithdrawNonceAccount, e.data);
          return {
            noncePubkey: e.keys[0].pubkey,
            toPubkey: e.keys[1].pubkey,
            authorizedPubkey: e.keys[4].pubkey,
            lamports: t,
          };
        }
        static decodeNonceAuthorize(e) {
          this.checkProgramId(e.programId), this.checkKeyLength(e.keys, 2);
          let { authorized: t } = eS(eW.AuthorizeNonceAccount, e.data);
          return {
            noncePubkey: e.keys[0].pubkey,
            authorizedPubkey: e.keys[1].pubkey,
            newAuthorizedPubkey: new R(t),
          };
        }
        static checkProgramId(e) {
          if (!e.equals(eT.programId))
            throw Error("invalid instruction; programId is not SystemProgram");
        }
        static checkKeyLength(e, t) {
          if (e.length < t)
            throw Error(
              `invalid instruction; found ${e.length} keys, expected at least ${t}`
            );
        }
      }
      let eW = Object.freeze({
        Create: {
          index: 0,
          layout: h.w3([
            h.DH("instruction"),
            h.Wg("lamports"),
            h.Wg("space"),
            U("programId"),
          ]),
        },
        Assign: {
          index: 1,
          layout: h.w3([h.DH("instruction"), U("programId")]),
        },
        Transfer: {
          index: 2,
          layout: h.w3([h.DH("instruction"), eE("lamports")]),
        },
        CreateWithSeed: {
          index: 3,
          layout: h.w3([
            h.DH("instruction"),
            U("base"),
            V("seed"),
            h.Wg("lamports"),
            h.Wg("space"),
            U("programId"),
          ]),
        },
        AdvanceNonceAccount: {
          index: 4,
          layout: h.w3([h.DH("instruction")]),
        },
        WithdrawNonceAccount: {
          index: 5,
          layout: h.w3([h.DH("instruction"), h.Wg("lamports")]),
        },
        InitializeNonceAccount: {
          index: 6,
          layout: h.w3([h.DH("instruction"), U("authorized")]),
        },
        AuthorizeNonceAccount: {
          index: 7,
          layout: h.w3([h.DH("instruction"), U("authorized")]),
        },
        Allocate: {
          index: 8,
          layout: h.w3([h.DH("instruction"), h.Wg("space")]),
        },
        AllocateWithSeed: {
          index: 9,
          layout: h.w3([
            h.DH("instruction"),
            U("base"),
            V("seed"),
            h.Wg("space"),
            U("programId"),
          ]),
        },
        AssignWithSeed: {
          index: 10,
          layout: h.w3([
            h.DH("instruction"),
            U("base"),
            V("seed"),
            U("programId"),
          ]),
        },
        TransferWithSeed: {
          index: 11,
          layout: h.w3([
            h.DH("instruction"),
            eE("lamports"),
            V("seed"),
            U("programId"),
          ]),
        },
        UpgradeNonceAccount: {
          index: 12,
          layout: h.w3([h.DH("instruction")]),
        },
      });
      class eT {
        constructor() {}
        static createAccount(e) {
          let t = ew(eW.Create, {
            lamports: e.lamports,
            space: e.space,
            programId: E(e.programId.toBuffer()),
          });
          return new en({
            keys: [
              {
                pubkey: e.fromPubkey,
                isSigner: !0,
                isWritable: !0,
              },
              {
                pubkey: e.newAccountPubkey,
                isSigner: !0,
                isWritable: !0,
              },
            ],
            programId: this.programId,
            data: t,
          });
        }
        static transfer(e) {
          let t, r;
          return (
            "basePubkey" in e
              ? ((t = ew(eW.TransferWithSeed, {
                  lamports: BigInt(e.lamports),
                  seed: e.seed,
                  programId: E(e.programId.toBuffer()),
                })),
                (r = [
                  {
                    pubkey: e.fromPubkey,
                    isSigner: !1,
                    isWritable: !0,
                  },
                  {
                    pubkey: e.basePubkey,
                    isSigner: !0,
                    isWritable: !1,
                  },
                  {
                    pubkey: e.toPubkey,
                    isSigner: !1,
                    isWritable: !0,
                  },
                ]))
              : ((t = ew(eW.Transfer, {
                  lamports: BigInt(e.lamports),
                })),
                (r = [
                  {
                    pubkey: e.fromPubkey,
                    isSigner: !0,
                    isWritable: !0,
                  },
                  {
                    pubkey: e.toPubkey,
                    isSigner: !1,
                    isWritable: !0,
                  },
                ])),
            new en({
              keys: r,
              programId: this.programId,
              data: t,
            })
          );
        }
        static assign(e) {
          let t, r;
          return (
            "basePubkey" in e
              ? ((t = ew(eW.AssignWithSeed, {
                  base: E(e.basePubkey.toBuffer()),
                  seed: e.seed,
                  programId: E(e.programId.toBuffer()),
                })),
                (r = [
                  {
                    pubkey: e.accountPubkey,
                    isSigner: !1,
                    isWritable: !0,
                  },
                  {
                    pubkey: e.basePubkey,
                    isSigner: !0,
                    isWritable: !1,
                  },
                ]))
              : ((t = ew(eW.Assign, {
                  programId: E(e.programId.toBuffer()),
                })),
                (r = [
                  {
                    pubkey: e.accountPubkey,
                    isSigner: !0,
                    isWritable: !0,
                  },
                ])),
            new en({
              keys: r,
              programId: this.programId,
              data: t,
            })
          );
        }
        static createAccountWithSeed(e) {
          let t = ew(eW.CreateWithSeed, {
              base: E(e.basePubkey.toBuffer()),
              seed: e.seed,
              lamports: e.lamports,
              space: e.space,
              programId: E(e.programId.toBuffer()),
            }),
            r = [
              {
                pubkey: e.fromPubkey,
                isSigner: !0,
                isWritable: !0,
              },
              {
                pubkey: e.newAccountPubkey,
                isSigner: !1,
                isWritable: !0,
              },
            ];
          return (
            e.basePubkey != e.fromPubkey &&
              r.push({
                pubkey: e.basePubkey,
                isSigner: !0,
                isWritable: !1,
              }),
            new en({
              keys: r,
              programId: this.programId,
              data: t,
            })
          );
        }
        static createNonceAccount(e) {
          let t = new eo();
          "basePubkey" in e && "seed" in e
            ? t.add(
                eT.createAccountWithSeed({
                  fromPubkey: e.fromPubkey,
                  newAccountPubkey: e.noncePubkey,
                  basePubkey: e.basePubkey,
                  seed: e.seed,
                  lamports: e.lamports,
                  space: ev,
                  programId: this.programId,
                })
              )
            : t.add(
                eT.createAccount({
                  fromPubkey: e.fromPubkey,
                  newAccountPubkey: e.noncePubkey,
                  lamports: e.lamports,
                  space: ev,
                  programId: this.programId,
                })
              );
          let r = {
            noncePubkey: e.noncePubkey,
            authorizedPubkey: e.authorizedPubkey,
          };
          return t.add(this.nonceInitialize(r)), t;
        }
        static nonceInitialize(e) {
          let t = ew(eW.InitializeNonceAccount, {
            authorized: E(e.authorizedPubkey.toBuffer()),
          });
          return new en({
            keys: [
              {
                pubkey: e.noncePubkey,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: eh,
                isSigner: !1,
                isWritable: !1,
              },
              {
                pubkey: eg,
                isSigner: !1,
                isWritable: !1,
              },
            ],
            programId: this.programId,
            data: t,
          });
        }
        static nonceAdvance(e) {
          let t = ew(eW.AdvanceNonceAccount);
          return new en({
            keys: [
              {
                pubkey: e.noncePubkey,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: eh,
                isSigner: !1,
                isWritable: !1,
              },
              {
                pubkey: e.authorizedPubkey,
                isSigner: !0,
                isWritable: !1,
              },
            ],
            programId: this.programId,
            data: t,
          });
        }
        static nonceWithdraw(e) {
          let t = ew(eW.WithdrawNonceAccount, {
            lamports: e.lamports,
          });
          return new en({
            keys: [
              {
                pubkey: e.noncePubkey,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: e.toPubkey,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: eh,
                isSigner: !1,
                isWritable: !1,
              },
              {
                pubkey: eg,
                isSigner: !1,
                isWritable: !1,
              },
              {
                pubkey: e.authorizedPubkey,
                isSigner: !0,
                isWritable: !1,
              },
            ],
            programId: this.programId,
            data: t,
          });
        }
        static nonceAuthorize(e) {
          let t = ew(eW.AuthorizeNonceAccount, {
            authorized: E(e.newAuthorizedPubkey.toBuffer()),
          });
          return new en({
            keys: [
              {
                pubkey: e.noncePubkey,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: e.authorizedPubkey,
                isSigner: !0,
                isWritable: !1,
              },
            ],
            programId: this.programId,
            data: t,
          });
        }
        static allocate(e) {
          let t, r;
          return (
            "basePubkey" in e
              ? ((t = ew(eW.AllocateWithSeed, {
                  base: E(e.basePubkey.toBuffer()),
                  seed: e.seed,
                  space: e.space,
                  programId: E(e.programId.toBuffer()),
                })),
                (r = [
                  {
                    pubkey: e.accountPubkey,
                    isSigner: !1,
                    isWritable: !0,
                  },
                  {
                    pubkey: e.basePubkey,
                    isSigner: !0,
                    isWritable: !1,
                  },
                ]))
              : ((t = ew(eW.Allocate, {
                  space: e.space,
                })),
                (r = [
                  {
                    pubkey: e.accountPubkey,
                    isSigner: !0,
                    isWritable: !0,
                  },
                ])),
            new en({
              keys: r,
              programId: this.programId,
              data: t,
            })
          );
        }
      }
      eT.programId = new R("********************************");
      let eN = K - 300;
      class eO {
        constructor() {}
        static getMinNumSignatures(e) {
          return 2 * (Math.ceil(e / eO.chunkSize) + 1 + 1);
        }
        static async load(e, t, r, i, n) {
          {
            let s = await e.getMinimumBalanceForRentExemption(n.length),
              o = await e.getAccountInfo(r.publicKey, "confirmed"),
              a = null;
            if (null !== o) {
              if (o.executable)
                return (
                  console.error(
                    "Program load failed, account is already executable"
                  ),
                  !1
                );
              o.data.length !== n.length &&
                (a = a || new eo()).add(
                  eT.allocate({
                    accountPubkey: r.publicKey,
                    space: n.length,
                  })
                ),
                o.owner.equals(i) ||
                  (a = a || new eo()).add(
                    eT.assign({
                      accountPubkey: r.publicKey,
                      programId: i,
                    })
                  ),
                o.lamports < s &&
                  (a = a || new eo()).add(
                    eT.transfer({
                      fromPubkey: t.publicKey,
                      toPubkey: r.publicKey,
                      lamports: s - o.lamports,
                    })
                  );
            } else
              a = new eo().add(
                eT.createAccount({
                  fromPubkey: t.publicKey,
                  newAccountPubkey: r.publicKey,
                  lamports: s > 0 ? s : 1,
                  space: n.length,
                  programId: i,
                })
              );
            null !== a &&
              (await ef(e, a, [t, r], {
                commitment: "confirmed",
              }));
          }
          let o = h.w3([
              h.DH("instruction"),
              h.DH("offset"),
              h.DH("bytesLength"),
              h.DH("bytesLengthPadding"),
              h.O6(h.u8("byte"), h.cY(h.DH(), -8), "bytes"),
            ]),
            a = eO.chunkSize,
            c = 0,
            u = n,
            l = [];
          for (; u.length > 0; ) {
            let n = u.slice(0, a),
              d = s.Buffer.alloc(a + 16);
            o.encode(
              {
                instruction: 0,
                offset: c,
                bytes: n,
                bytesLength: 0,
                bytesLengthPadding: 0,
              },
              d
            );
            let h = new eo().add({
              keys: [
                {
                  pubkey: r.publicKey,
                  isSigner: !0,
                  isWritable: !0,
                },
              ],
              programId: i,
              data: d,
            });
            l.push(
              ef(e, h, [t, r], {
                commitment: "confirmed",
              })
            ),
              e._rpcEndpoint.includes("solana.com") && (await ek(250)),
              (c += a),
              (u = u.slice(a));
          }
          await Promise.all(l);
          {
            let n = h.w3([h.DH("instruction")]),
              o = s.Buffer.alloc(n.span);
            n.encode(
              {
                instruction: 1,
              },
              o
            );
            let a = new eo().add({
                keys: [
                  {
                    pubkey: r.publicKey,
                    isSigner: !0,
                    isWritable: !0,
                  },
                  {
                    pubkey: eg,
                    isSigner: !1,
                    isWritable: !1,
                  },
                ],
                programId: i,
                data: o,
              }),
              c = "processed",
              u = await e.sendTransaction(a, [t, r], {
                preflightCommitment: c,
              }),
              { context: l, value: d } = await e.confirmTransaction(
                {
                  signature: u,
                  lastValidBlockHeight: a.lastValidBlockHeight,
                  blockhash: a.recentBlockhash,
                },
                c
              );
            if (d.err)
              throw Error(`Transaction ${u} failed (${JSON.stringify(d)})`);
            for (;;) {
              try {
                if (
                  (await e.getSlot({
                    commitment: c,
                  })) > l.slot
                )
                  break;
              } catch {}
              await new Promise((e) => setTimeout(e, Math.round(200)));
            }
          }
          return !0;
        }
      }
      eO.chunkSize = eN;
      let eC = new R("BPFLoader2********************************1");
      class eR {
        static getMinNumSignatures(e) {
          return eO.getMinNumSignatures(e);
        }
        static load(e, t, r, i, s) {
          return eO.load(e, t, r, s, i);
        }
      }
      var ex = Object.prototype.toString,
        eL =
          Object.keys ||
          function (e) {
            var t = [];
            for (var r in e) t.push(r);
            return t;
          },
        eK = (function (e) {
          return e &&
            e.__esModule &&
            Object.prototype.hasOwnProperty.call(e, "default")
            ? e.default
            : e;
        })(function (e) {
          var t = (function e(t, r) {
            var i, s, n, o, a, c, u;
            if (!0 === t) return "true";
            if (!1 === t) return "false";
            switch (typeof t) {
              case "object":
                if (null === t) return null;
                if (t.toJSON && "function" == typeof t.toJSON)
                  return e(t.toJSON(), r);
                if ("[object Array]" === (u = ex.call(t))) {
                  for (i = 0, n = "[", s = t.length - 1; i < s; i++)
                    n += e(t[i], !0) + ",";
                  return s > -1 && (n += e(t[i], !0)), n + "]";
                }
                if ("[object Object]" !== u) return JSON.stringify(t);
                for (s = (o = eL(t).sort()).length, n = "", i = 0; i < s; )
                  void 0 !== (c = e(t[(a = o[i])], !1)) &&
                    (n && (n += ","), (n += JSON.stringify(a) + ":" + c)),
                    i++;
                return "{" + n + "}";
              case "function":
              case "undefined":
                return r ? null : void 0;
              case "string":
                return JSON.stringify(t);
              default:
                return isFinite(t) ? t : null;
            }
          })(e, !1);
          if (void 0 !== t) return "" + t;
        });
      function ez(e) {
        let t = 0;
        for (; e > 1; ) (e /= 2), t++;
        return t;
      }
      class eY {
        constructor(e, t, r, i, s) {
          (this.slotsPerEpoch = void 0),
            (this.leaderScheduleSlotOffset = void 0),
            (this.warmup = void 0),
            (this.firstNormalEpoch = void 0),
            (this.firstNormalSlot = void 0),
            (this.slotsPerEpoch = e),
            (this.leaderScheduleSlotOffset = t),
            (this.warmup = r),
            (this.firstNormalEpoch = i),
            (this.firstNormalSlot = s);
        }
        getEpoch(e) {
          return this.getEpochAndSlotIndex(e)[0];
        }
        getEpochAndSlotIndex(e) {
          if (e < this.firstNormalSlot) {
            var t;
            let r =
                ez(
                  0 === (t = e + 32 + 1)
                    ? 1
                    : (t--,
                      (t |= t >> 1),
                      (t |= t >> 2),
                      (t |= t >> 4),
                      (t |= t >> 8),
                      (t |= t >> 16),
                      (t |= t >> 32) + 1)
                ) -
                ez(32) -
                1,
              i = this.getSlotsInEpoch(r);
            return [r, e - (i - 32)];
          }
          {
            let t = e - this.firstNormalSlot,
              r = Math.floor(t / this.slotsPerEpoch);
            return [this.firstNormalEpoch + r, t % this.slotsPerEpoch];
          }
        }
        getFirstSlotInEpoch(e) {
          return e <= this.firstNormalEpoch
            ? (Math.pow(2, e) - 1) * 32
            : (e - this.firstNormalEpoch) * this.slotsPerEpoch +
                this.firstNormalSlot;
        }
        getLastSlotInEpoch(e) {
          return this.getFirstSlotInEpoch(e) + this.getSlotsInEpoch(e) - 1;
        }
        getSlotsInEpoch(e) {
          return e < this.firstNormalEpoch
            ? Math.pow(2, e + ez(32))
            : this.slotsPerEpoch;
        }
      }
      class eq extends Error {
        constructor(e, t) {
          super(e), (this.logs = void 0), (this.logs = t);
        }
      }
      let eD = {
        JSON_RPC_SERVER_ERROR_BLOCK_CLEANED_UP: -32001,
        JSON_RPC_SERVER_ERROR_SEND_TRANSACTION_PREFLIGHT_FAILURE: -32002,
        JSON_RPC_SERVER_ERROR_TRANSACTION_SIGNATURE_VERIFICATION_FAILURE:
          -32003,
        JSON_RPC_SERVER_ERROR_BLOCK_NOT_AVAILABLE: -32004,
        JSON_RPC_SERVER_ERROR_NODE_UNHEALTHY: -32005,
        JSON_RPC_SERVER_ERROR_TRANSACTION_PRECOMPILE_VERIFICATION_FAILURE:
          -32006,
        JSON_RPC_SERVER_ERROR_SLOT_SKIPPED: -32007,
        JSON_RPC_SERVER_ERROR_NO_SNAPSHOT: -32008,
        JSON_RPC_SERVER_ERROR_LONG_TERM_STORAGE_SLOT_SKIPPED: -32009,
        JSON_RPC_SERVER_ERROR_KEY_EXCLUDED_FROM_SECONDARY_INDEX: -32010,
        JSON_RPC_SERVER_ERROR_TRANSACTION_HISTORY_NOT_AVAILABLE: -32011,
        JSON_RPC_SCAN_ERROR: -32012,
        JSON_RPC_SERVER_ERROR_TRANSACTION_SIGNATURE_LEN_MISMATCH: -32013,
        JSON_RPC_SERVER_ERROR_BLOCK_STATUS_NOT_AVAILABLE_YET: -32014,
        JSON_RPC_SERVER_ERROR_UNSUPPORTED_TRANSACTION_VERSION: -32015,
        JSON_RPC_SERVER_ERROR_MIN_CONTEXT_SLOT_NOT_REACHED: -32016,
      };
      class eH extends Error {
        constructor({ code: e, message: t, data: r }, i) {
          super(null != i ? `${i}: ${t}` : t),
            (this.code = void 0),
            (this.data = void 0),
            (this.code = e),
            (this.data = r),
            (this.name = "SolanaJSONRPCError");
        }
      }
      var ej = globalThis.fetch;
      class eU extends b.A {
        constructor(e, t, r) {
          super(
            (e) => {
              let r = (0, f.A)(e, {
                autoconnect: !0,
                max_reconnects: 5,
                reconnect: !0,
                reconnect_interval: 1e3,
                ...t,
              });
              return (
                "socket" in r
                  ? (this.underlyingSocket = r.socket)
                  : (this.underlyingSocket = r),
                r
              );
            },
            e,
            t,
            r
          ),
            (this.underlyingSocket = void 0);
        }
        call(...e) {
          let t = this.underlyingSocket?.readyState;
          return 1 === t
            ? super.call(...e)
            : Promise.reject(
                Error(
                  "Tried to call a JSON-RPC method `" +
                    e[0] +
                    "` but the socket was not `CONNECTING` or `OPEN` (`readyState` was " +
                    t +
                    ")"
                )
              );
        }
        notify(...e) {
          let t = this.underlyingSocket?.readyState;
          return 1 === t
            ? super.notify(...e)
            : Promise.reject(
                Error(
                  "Tried to send a JSON-RPC notification `" +
                    e[0] +
                    "` but the socket was not `CONNECTING` or `OPEN` (`readyState` was " +
                    t +
                    ")"
                )
              );
        }
      }
      class eM {
        constructor(e) {
          (this.key = void 0),
            (this.state = void 0),
            (this.key = e.key),
            (this.state = e.state);
        }
        isActive() {
          let e = BigInt("0xffffffffffffffff");
          return this.state.deactivationSlot === e;
        }
        static deserialize(e) {
          let t = (function (e, t) {
              let r;
              try {
                r = e.layout.decode(t);
              } catch (e) {
                throw Error("invalid instruction; " + e);
              }
              if (r.typeIndex !== e.index)
                throw Error(
                  `invalid account data; account type mismatch ${r.typeIndex} != ${e.index}`
                );
              return r;
            })(eV, e),
            r = e.length - 56;
          J(r >= 0, "lookup table is invalid"),
            J(r % 32 == 0, "lookup table is invalid");
          let { addresses: i } = h
            .w3([h.O6(U(), r / 32, "addresses")])
            .decode(e.slice(56));
          return {
            deactivationSlot: t.deactivationSlot,
            lastExtendedSlot: t.lastExtendedSlot,
            lastExtendedSlotStartIndex: t.lastExtendedStartIndex,
            authority:
              0 !== t.authority.length ? new R(t.authority[0]) : void 0,
            addresses: i.map((e) => new R(e)),
          };
        }
      }
      let eV = {
          index: 1,
          layout: h.w3([
            h.DH("typeIndex"),
            eE("deactivationSlot"),
            h.I0("lastExtendedSlot"),
            h.u8("lastExtendedStartIndex"),
            h.u8(),
            h.O6(U(), h.cY(h.u8(), -1), "authority"),
          ]),
        },
        eF = /^[^:]+:\/\/([^:[]+|\[[^\]]+\])(:\d+)?(.*)/i,
        e$ = (0, p.au)((0, p.KJ)(R), (0, p.Yj)(), (e) => new R(e)),
        eJ = (0, p.PV)([(0, p.Yj)(), (0, p.eu)("base64")]),
        eG = (0, p.au)((0, p.KJ)(s.Buffer), eJ, (e) =>
          s.Buffer.from(e[0], "base64")
        ),
        eX = 3e4;
      function eZ(e) {
        let t, r;
        if ("string" == typeof e) t = e;
        else if (e) {
          let { commitment: i, ...s } = e;
          (t = i), (r = s);
        }
        return {
          commitment: t,
          config: r,
        };
      }
      function eQ(e) {
        return (0, p.KC)([
          (0, p.NW)({
            jsonrpc: (0, p.eu)("2.0"),
            id: (0, p.Yj)(),
            result: e,
          }),
          (0, p.NW)({
            jsonrpc: (0, p.eu)("2.0"),
            id: (0, p.Yj)(),
            error: (0, p.NW)({
              code: (0, p.L5)(),
              message: (0, p.Yj)(),
              data: (0, p.lq)((0, p.bz)()),
            }),
          }),
        ]);
      }
      let e0 = eQ((0, p.L5)());
      function e1(e) {
        return (0, p.au)(eQ(e), e0, (t) =>
          "error" in t
            ? t
            : {
                ...t,
                result: (0, p.vt)(t.result, e),
              }
        );
      }
      function e3(e) {
        return e1(
          (0, p.NW)({
            context: (0, p.NW)({
              slot: (0, p.ai)(),
            }),
            value: e,
          })
        );
      }
      function e8(e) {
        return (0, p.NW)({
          context: (0, p.NW)({
            slot: (0, p.ai)(),
          }),
          value: e,
        });
      }
      function e2(e, t) {
        return 0 === e
          ? new et({
              header: t.header,
              staticAccountKeys: t.accountKeys.map((e) => new R(e)),
              recentBlockhash: t.recentBlockhash,
              compiledInstructions: t.instructions.map((e) => ({
                programIdIndex: e.programIdIndex,
                accountKeyIndexes: e.accounts,
                data: u().decode(e.data),
              })),
              addressTableLookups: t.addressTableLookups,
            })
          : new ee(t);
      }
      let e5 = (0, p.NW)({
          foundation: (0, p.ai)(),
          foundationTerm: (0, p.ai)(),
          initial: (0, p.ai)(),
          taper: (0, p.ai)(),
          terminal: (0, p.ai)(),
        }),
        e6 = e1(
          (0, p.YO)(
            (0, p.me)(
              (0, p.NW)({
                epoch: (0, p.ai)(),
                effectiveSlot: (0, p.ai)(),
                amount: (0, p.ai)(),
                postBalance: (0, p.ai)(),
                commission: (0, p.lq)((0, p.me)((0, p.ai)())),
              })
            )
          )
        ),
        e4 = (0, p.YO)(
          (0, p.NW)({
            slot: (0, p.ai)(),
            prioritizationFee: (0, p.ai)(),
          })
        ),
        e7 = (0, p.NW)({
          total: (0, p.ai)(),
          validator: (0, p.ai)(),
          foundation: (0, p.ai)(),
          epoch: (0, p.ai)(),
        }),
        e9 = (0, p.NW)({
          epoch: (0, p.ai)(),
          slotIndex: (0, p.ai)(),
          slotsInEpoch: (0, p.ai)(),
          absoluteSlot: (0, p.ai)(),
          blockHeight: (0, p.lq)((0, p.ai)()),
          transactionCount: (0, p.lq)((0, p.ai)()),
        }),
        te = (0, p.NW)({
          slotsPerEpoch: (0, p.ai)(),
          leaderScheduleSlotOffset: (0, p.ai)(),
          warmup: (0, p.zM)(),
          firstNormalEpoch: (0, p.ai)(),
          firstNormalSlot: (0, p.ai)(),
        }),
        tt = (0, p.g1)((0, p.Yj)(), (0, p.YO)((0, p.ai)())),
        tr = (0, p.me)((0, p.KC)([(0, p.NW)({}), (0, p.Yj)()])),
        ti = (0, p.NW)({
          err: tr,
        }),
        ts = (0, p.eu)("receivedSignature"),
        tn = (0, p.NW)({
          "solana-core": (0, p.Yj)(),
          "feature-set": (0, p.lq)((0, p.ai)()),
        }),
        to = e3(
          (0, p.NW)({
            err: (0, p.me)((0, p.KC)([(0, p.NW)({}), (0, p.Yj)()])),
            logs: (0, p.me)((0, p.YO)((0, p.Yj)())),
            accounts: (0, p.lq)(
              (0, p.me)(
                (0, p.YO)(
                  (0, p.me)(
                    (0, p.NW)({
                      executable: (0, p.zM)(),
                      owner: (0, p.Yj)(),
                      lamports: (0, p.ai)(),
                      data: (0, p.YO)((0, p.Yj)()),
                      rentEpoch: (0, p.lq)((0, p.ai)()),
                    })
                  )
                )
              )
            ),
            unitsConsumed: (0, p.lq)((0, p.ai)()),
            returnData: (0, p.lq)(
              (0, p.me)(
                (0, p.NW)({
                  programId: (0, p.Yj)(),
                  data: (0, p.PV)([(0, p.Yj)(), (0, p.eu)("base64")]),
                })
              )
            ),
          })
        ),
        ta = e3(
          (0, p.NW)({
            byIdentity: (0, p.g1)((0, p.Yj)(), (0, p.YO)((0, p.ai)())),
            range: (0, p.NW)({
              firstSlot: (0, p.ai)(),
              lastSlot: (0, p.ai)(),
            }),
          })
        ),
        tc = e1(e5),
        tu = e1(e7),
        tl = e1(e4),
        td = e1(e9),
        th = e1(te),
        tg = e1(tt),
        tp = e1((0, p.ai)()),
        tm = e3(
          (0, p.NW)({
            total: (0, p.ai)(),
            circulating: (0, p.ai)(),
            nonCirculating: (0, p.ai)(),
            nonCirculatingAccounts: (0, p.YO)(e$),
          })
        ),
        ty = (0, p.NW)({
          amount: (0, p.Yj)(),
          uiAmount: (0, p.me)((0, p.ai)()),
          decimals: (0, p.ai)(),
          uiAmountString: (0, p.lq)((0, p.Yj)()),
        }),
        tb = e3(
          (0, p.YO)(
            (0, p.NW)({
              address: e$,
              amount: (0, p.Yj)(),
              uiAmount: (0, p.me)((0, p.ai)()),
              decimals: (0, p.ai)(),
              uiAmountString: (0, p.lq)((0, p.Yj)()),
            })
          )
        ),
        tf = e3(
          (0, p.YO)(
            (0, p.NW)({
              pubkey: e$,
              account: (0, p.NW)({
                executable: (0, p.zM)(),
                owner: e$,
                lamports: (0, p.ai)(),
                data: eG,
                rentEpoch: (0, p.ai)(),
              }),
            })
          )
        ),
        tk = (0, p.NW)({
          program: (0, p.Yj)(),
          parsed: (0, p.L5)(),
          space: (0, p.ai)(),
        }),
        tw = e3(
          (0, p.YO)(
            (0, p.NW)({
              pubkey: e$,
              account: (0, p.NW)({
                executable: (0, p.zM)(),
                owner: e$,
                lamports: (0, p.ai)(),
                data: tk,
                rentEpoch: (0, p.ai)(),
              }),
            })
          )
        ),
        tS = e3(
          (0, p.YO)(
            (0, p.NW)({
              lamports: (0, p.ai)(),
              address: e$,
            })
          )
        ),
        tI = (0, p.NW)({
          executable: (0, p.zM)(),
          owner: e$,
          lamports: (0, p.ai)(),
          data: eG,
          rentEpoch: (0, p.ai)(),
        }),
        t_ = (0, p.NW)({
          pubkey: e$,
          account: tI,
        }),
        tv = (0, p.au)(
          (0, p.KC)([(0, p.KJ)(s.Buffer), tk]),
          (0, p.KC)([eJ, tk]),
          (e) => (Array.isArray(e) ? (0, p.vt)(e, eG) : e)
        ),
        tA = (0, p.NW)({
          executable: (0, p.zM)(),
          owner: e$,
          lamports: (0, p.ai)(),
          data: tv,
          rentEpoch: (0, p.ai)(),
        }),
        tP = (0, p.NW)({
          pubkey: e$,
          account: tA,
        }),
        tE = (0, p.NW)({
          state: (0, p.KC)([
            (0, p.eu)("active"),
            (0, p.eu)("inactive"),
            (0, p.eu)("activating"),
            (0, p.eu)("deactivating"),
          ]),
          active: (0, p.ai)(),
          inactive: (0, p.ai)(),
        }),
        tB = e1(
          (0, p.YO)(
            (0, p.NW)({
              signature: (0, p.Yj)(),
              slot: (0, p.ai)(),
              err: tr,
              memo: (0, p.me)((0, p.Yj)()),
              blockTime: (0, p.lq)((0, p.me)((0, p.ai)())),
            })
          )
        ),
        tW = e1(
          (0, p.YO)(
            (0, p.NW)({
              signature: (0, p.Yj)(),
              slot: (0, p.ai)(),
              err: tr,
              memo: (0, p.me)((0, p.Yj)()),
              blockTime: (0, p.lq)((0, p.me)((0, p.ai)())),
            })
          )
        ),
        tT = (0, p.NW)({
          subscription: (0, p.ai)(),
          result: e8(tI),
        }),
        tN = (0, p.NW)({
          pubkey: e$,
          account: tI,
        }),
        tO = (0, p.NW)({
          subscription: (0, p.ai)(),
          result: e8(tN),
        }),
        tC = (0, p.NW)({
          parent: (0, p.ai)(),
          slot: (0, p.ai)(),
          root: (0, p.ai)(),
        }),
        tR = (0, p.NW)({
          subscription: (0, p.ai)(),
          result: tC,
        }),
        tx = (0, p.KC)([
          (0, p.NW)({
            type: (0, p.KC)([
              (0, p.eu)("firstShredReceived"),
              (0, p.eu)("completed"),
              (0, p.eu)("optimisticConfirmation"),
              (0, p.eu)("root"),
            ]),
            slot: (0, p.ai)(),
            timestamp: (0, p.ai)(),
          }),
          (0, p.NW)({
            type: (0, p.eu)("createdBank"),
            parent: (0, p.ai)(),
            slot: (0, p.ai)(),
            timestamp: (0, p.ai)(),
          }),
          (0, p.NW)({
            type: (0, p.eu)("frozen"),
            slot: (0, p.ai)(),
            timestamp: (0, p.ai)(),
            stats: (0, p.NW)({
              numTransactionEntries: (0, p.ai)(),
              numSuccessfulTransactions: (0, p.ai)(),
              numFailedTransactions: (0, p.ai)(),
              maxTransactionsPerEntry: (0, p.ai)(),
            }),
          }),
          (0, p.NW)({
            type: (0, p.eu)("dead"),
            slot: (0, p.ai)(),
            timestamp: (0, p.ai)(),
            err: (0, p.Yj)(),
          }),
        ]),
        tL = (0, p.NW)({
          subscription: (0, p.ai)(),
          result: tx,
        }),
        tK = (0, p.NW)({
          subscription: (0, p.ai)(),
          result: e8((0, p.KC)([ti, ts])),
        }),
        tz = (0, p.NW)({
          subscription: (0, p.ai)(),
          result: (0, p.ai)(),
        }),
        tY = (0, p.NW)({
          pubkey: (0, p.Yj)(),
          gossip: (0, p.me)((0, p.Yj)()),
          tpu: (0, p.me)((0, p.Yj)()),
          rpc: (0, p.me)((0, p.Yj)()),
          version: (0, p.me)((0, p.Yj)()),
        }),
        tq = (0, p.NW)({
          votePubkey: (0, p.Yj)(),
          nodePubkey: (0, p.Yj)(),
          activatedStake: (0, p.ai)(),
          epochVoteAccount: (0, p.zM)(),
          epochCredits: (0, p.YO)(
            (0, p.PV)([(0, p.ai)(), (0, p.ai)(), (0, p.ai)()])
          ),
          commission: (0, p.ai)(),
          lastVote: (0, p.ai)(),
          rootSlot: (0, p.me)((0, p.ai)()),
        }),
        tD = e1(
          (0, p.NW)({
            current: (0, p.YO)(tq),
            delinquent: (0, p.YO)(tq),
          })
        ),
        tH = (0, p.KC)([
          (0, p.eu)("processed"),
          (0, p.eu)("confirmed"),
          (0, p.eu)("finalized"),
        ]),
        tj = (0, p.NW)({
          slot: (0, p.ai)(),
          confirmations: (0, p.me)((0, p.ai)()),
          err: tr,
          confirmationStatus: (0, p.lq)(tH),
        }),
        tU = e3((0, p.YO)((0, p.me)(tj))),
        tM = e1((0, p.ai)()),
        tV = (0, p.NW)({
          accountKey: e$,
          writableIndexes: (0, p.YO)((0, p.ai)()),
          readonlyIndexes: (0, p.YO)((0, p.ai)()),
        }),
        tF = (0, p.NW)({
          signatures: (0, p.YO)((0, p.Yj)()),
          message: (0, p.NW)({
            accountKeys: (0, p.YO)((0, p.Yj)()),
            header: (0, p.NW)({
              numRequiredSignatures: (0, p.ai)(),
              numReadonlySignedAccounts: (0, p.ai)(),
              numReadonlyUnsignedAccounts: (0, p.ai)(),
            }),
            instructions: (0, p.YO)(
              (0, p.NW)({
                accounts: (0, p.YO)((0, p.ai)()),
                data: (0, p.Yj)(),
                programIdIndex: (0, p.ai)(),
              })
            ),
            recentBlockhash: (0, p.Yj)(),
            addressTableLookups: (0, p.lq)((0, p.YO)(tV)),
          }),
        }),
        t$ = (0, p.NW)({
          pubkey: e$,
          signer: (0, p.zM)(),
          writable: (0, p.zM)(),
          source: (0, p.lq)(
            (0, p.KC)([(0, p.eu)("transaction"), (0, p.eu)("lookupTable")])
          ),
        }),
        tJ = (0, p.NW)({
          accountKeys: (0, p.YO)(t$),
          signatures: (0, p.YO)((0, p.Yj)()),
        }),
        tG = (0, p.NW)({
          parsed: (0, p.L5)(),
          program: (0, p.Yj)(),
          programId: e$,
        }),
        tX = (0, p.NW)({
          accounts: (0, p.YO)(e$),
          data: (0, p.Yj)(),
          programId: e$,
        }),
        tZ = (0, p.KC)([tX, tG]),
        tQ = (0, p.KC)([
          (0, p.NW)({
            parsed: (0, p.L5)(),
            program: (0, p.Yj)(),
            programId: (0, p.Yj)(),
          }),
          (0, p.NW)({
            accounts: (0, p.YO)((0, p.Yj)()),
            data: (0, p.Yj)(),
            programId: (0, p.Yj)(),
          }),
        ]),
        t0 = (0, p.au)(tZ, tQ, (e) =>
          "accounts" in e ? (0, p.vt)(e, tX) : (0, p.vt)(e, tG)
        ),
        t1 = (0, p.NW)({
          signatures: (0, p.YO)((0, p.Yj)()),
          message: (0, p.NW)({
            accountKeys: (0, p.YO)(t$),
            instructions: (0, p.YO)(t0),
            recentBlockhash: (0, p.Yj)(),
            addressTableLookups: (0, p.lq)((0, p.me)((0, p.YO)(tV))),
          }),
        }),
        t3 = (0, p.NW)({
          accountIndex: (0, p.ai)(),
          mint: (0, p.Yj)(),
          owner: (0, p.lq)((0, p.Yj)()),
          uiTokenAmount: ty,
        }),
        t8 = (0, p.NW)({
          writable: (0, p.YO)(e$),
          readonly: (0, p.YO)(e$),
        }),
        t2 = (0, p.NW)({
          err: tr,
          fee: (0, p.ai)(),
          innerInstructions: (0, p.lq)(
            (0, p.me)(
              (0, p.YO)(
                (0, p.NW)({
                  index: (0, p.ai)(),
                  instructions: (0, p.YO)(
                    (0, p.NW)({
                      accounts: (0, p.YO)((0, p.ai)()),
                      data: (0, p.Yj)(),
                      programIdIndex: (0, p.ai)(),
                    })
                  ),
                })
              )
            )
          ),
          preBalances: (0, p.YO)((0, p.ai)()),
          postBalances: (0, p.YO)((0, p.ai)()),
          logMessages: (0, p.lq)((0, p.me)((0, p.YO)((0, p.Yj)()))),
          preTokenBalances: (0, p.lq)((0, p.me)((0, p.YO)(t3))),
          postTokenBalances: (0, p.lq)((0, p.me)((0, p.YO)(t3))),
          loadedAddresses: (0, p.lq)(t8),
          computeUnitsConsumed: (0, p.lq)((0, p.ai)()),
        }),
        t5 = (0, p.NW)({
          err: tr,
          fee: (0, p.ai)(),
          innerInstructions: (0, p.lq)(
            (0, p.me)(
              (0, p.YO)(
                (0, p.NW)({
                  index: (0, p.ai)(),
                  instructions: (0, p.YO)(t0),
                })
              )
            )
          ),
          preBalances: (0, p.YO)((0, p.ai)()),
          postBalances: (0, p.YO)((0, p.ai)()),
          logMessages: (0, p.lq)((0, p.me)((0, p.YO)((0, p.Yj)()))),
          preTokenBalances: (0, p.lq)((0, p.me)((0, p.YO)(t3))),
          postTokenBalances: (0, p.lq)((0, p.me)((0, p.YO)(t3))),
          loadedAddresses: (0, p.lq)(t8),
          computeUnitsConsumed: (0, p.lq)((0, p.ai)()),
        }),
        t6 = (0, p.KC)([(0, p.eu)(0), (0, p.eu)("legacy")]),
        t4 = (0, p.NW)({
          pubkey: (0, p.Yj)(),
          lamports: (0, p.ai)(),
          postBalance: (0, p.me)((0, p.ai)()),
          rewardType: (0, p.me)((0, p.Yj)()),
          commission: (0, p.lq)((0, p.me)((0, p.ai)())),
        }),
        t7 = e1(
          (0, p.me)(
            (0, p.NW)({
              blockhash: (0, p.Yj)(),
              previousBlockhash: (0, p.Yj)(),
              parentSlot: (0, p.ai)(),
              transactions: (0, p.YO)(
                (0, p.NW)({
                  transaction: tF,
                  meta: (0, p.me)(t2),
                  version: (0, p.lq)(t6),
                })
              ),
              rewards: (0, p.lq)((0, p.YO)(t4)),
              blockTime: (0, p.me)((0, p.ai)()),
              blockHeight: (0, p.me)((0, p.ai)()),
            })
          )
        ),
        t9 = e1(
          (0, p.me)(
            (0, p.NW)({
              blockhash: (0, p.Yj)(),
              previousBlockhash: (0, p.Yj)(),
              parentSlot: (0, p.ai)(),
              rewards: (0, p.lq)((0, p.YO)(t4)),
              blockTime: (0, p.me)((0, p.ai)()),
              blockHeight: (0, p.me)((0, p.ai)()),
            })
          )
        ),
        re = e1(
          (0, p.me)(
            (0, p.NW)({
              blockhash: (0, p.Yj)(),
              previousBlockhash: (0, p.Yj)(),
              parentSlot: (0, p.ai)(),
              transactions: (0, p.YO)(
                (0, p.NW)({
                  transaction: tJ,
                  meta: (0, p.me)(t2),
                  version: (0, p.lq)(t6),
                })
              ),
              rewards: (0, p.lq)((0, p.YO)(t4)),
              blockTime: (0, p.me)((0, p.ai)()),
              blockHeight: (0, p.me)((0, p.ai)()),
            })
          )
        ),
        rt = e1(
          (0, p.me)(
            (0, p.NW)({
              blockhash: (0, p.Yj)(),
              previousBlockhash: (0, p.Yj)(),
              parentSlot: (0, p.ai)(),
              transactions: (0, p.YO)(
                (0, p.NW)({
                  transaction: t1,
                  meta: (0, p.me)(t5),
                  version: (0, p.lq)(t6),
                })
              ),
              rewards: (0, p.lq)((0, p.YO)(t4)),
              blockTime: (0, p.me)((0, p.ai)()),
              blockHeight: (0, p.me)((0, p.ai)()),
            })
          )
        ),
        rr = e1(
          (0, p.me)(
            (0, p.NW)({
              blockhash: (0, p.Yj)(),
              previousBlockhash: (0, p.Yj)(),
              parentSlot: (0, p.ai)(),
              transactions: (0, p.YO)(
                (0, p.NW)({
                  transaction: tJ,
                  meta: (0, p.me)(t5),
                  version: (0, p.lq)(t6),
                })
              ),
              rewards: (0, p.lq)((0, p.YO)(t4)),
              blockTime: (0, p.me)((0, p.ai)()),
              blockHeight: (0, p.me)((0, p.ai)()),
            })
          )
        ),
        ri = e1(
          (0, p.me)(
            (0, p.NW)({
              blockhash: (0, p.Yj)(),
              previousBlockhash: (0, p.Yj)(),
              parentSlot: (0, p.ai)(),
              rewards: (0, p.lq)((0, p.YO)(t4)),
              blockTime: (0, p.me)((0, p.ai)()),
              blockHeight: (0, p.me)((0, p.ai)()),
            })
          )
        ),
        rs = e1(
          (0, p.me)(
            (0, p.NW)({
              blockhash: (0, p.Yj)(),
              previousBlockhash: (0, p.Yj)(),
              parentSlot: (0, p.ai)(),
              transactions: (0, p.YO)(
                (0, p.NW)({
                  transaction: tF,
                  meta: (0, p.me)(t2),
                })
              ),
              rewards: (0, p.lq)((0, p.YO)(t4)),
              blockTime: (0, p.me)((0, p.ai)()),
            })
          )
        ),
        rn = e1(
          (0, p.me)(
            (0, p.NW)({
              blockhash: (0, p.Yj)(),
              previousBlockhash: (0, p.Yj)(),
              parentSlot: (0, p.ai)(),
              signatures: (0, p.YO)((0, p.Yj)()),
              blockTime: (0, p.me)((0, p.ai)()),
            })
          )
        ),
        ro = e1(
          (0, p.me)(
            (0, p.NW)({
              slot: (0, p.ai)(),
              meta: (0, p.me)(t2),
              blockTime: (0, p.lq)((0, p.me)((0, p.ai)())),
              transaction: tF,
              version: (0, p.lq)(t6),
            })
          )
        ),
        ra = e1(
          (0, p.me)(
            (0, p.NW)({
              slot: (0, p.ai)(),
              transaction: t1,
              meta: (0, p.me)(t5),
              blockTime: (0, p.lq)((0, p.me)((0, p.ai)())),
              version: (0, p.lq)(t6),
            })
          )
        ),
        rc = e3(
          (0, p.NW)({
            blockhash: (0, p.Yj)(),
            feeCalculator: (0, p.NW)({
              lamportsPerSignature: (0, p.ai)(),
            }),
          })
        ),
        ru = e3(
          (0, p.NW)({
            blockhash: (0, p.Yj)(),
            lastValidBlockHeight: (0, p.ai)(),
          })
        ),
        rl = e3((0, p.zM)()),
        rd = (0, p.NW)({
          slot: (0, p.ai)(),
          numTransactions: (0, p.ai)(),
          numSlots: (0, p.ai)(),
          samplePeriodSecs: (0, p.ai)(),
        }),
        rh = e1((0, p.YO)(rd)),
        rg = e3(
          (0, p.me)(
            (0, p.NW)({
              feeCalculator: (0, p.NW)({
                lamportsPerSignature: (0, p.ai)(),
              }),
            })
          )
        ),
        rp = e1((0, p.Yj)()),
        rm = e1((0, p.Yj)()),
        ry = (0, p.NW)({
          err: tr,
          logs: (0, p.YO)((0, p.Yj)()),
          signature: (0, p.Yj)(),
        }),
        rb = (0, p.NW)({
          result: e8(ry),
          subscription: (0, p.ai)(),
        }),
        rf = {
          "solana-client": "js/0.0.0-development",
        };
      class rk {
        constructor(e, t) {
          var r;
          let i, s, n, o, a, c;
          (this._commitment = void 0),
            (this._confirmTransactionInitialTimeout = void 0),
            (this._rpcEndpoint = void 0),
            (this._rpcWsEndpoint = void 0),
            (this._rpcClient = void 0),
            (this._rpcRequest = void 0),
            (this._rpcBatchRequest = void 0),
            (this._rpcWebSocket = void 0),
            (this._rpcWebSocketConnected = !1),
            (this._rpcWebSocketHeartbeat = null),
            (this._rpcWebSocketIdleTimeout = null),
            (this._rpcWebSocketGeneration = 0),
            (this._disableBlockhashCaching = !1),
            (this._pollingBlockhash = !1),
            (this._blockhashInfo = {
              latestBlockhash: null,
              lastFetch: 0,
              transactionSignatures: [],
              simulatedSignatures: [],
            }),
            (this._nextClientSubscriptionId = 0),
            (this._subscriptionDisposeFunctionsByClientSubscriptionId = {}),
            (this._subscriptionHashByClientSubscriptionId = {}),
            (this._subscriptionStateChangeCallbacksByHash = {}),
            (this._subscriptionCallbacksByServerSubscriptionId = {}),
            (this._subscriptionsByHash = {}),
            (this._subscriptionsAutoDisposedByRpc = new Set()),
            (this.getBlockHeight = (() => {
              let e = {};
              return async (t) => {
                let { commitment: r, config: i } = eZ(t),
                  s = this._buildArgs([], r, void 0, i),
                  n = eK(s);
                return (
                  (e[n] =
                    e[n] ??
                    (async () => {
                      try {
                        let e = await this._rpcRequest("getBlockHeight", s),
                          t = (0, p.vt)(e, e1((0, p.ai)()));
                        if ("error" in t)
                          throw new eH(
                            t.error,
                            "failed to get block height information"
                          );
                        return t.result;
                      } finally {
                        delete e[n];
                      }
                    })()),
                  await e[n]
                );
              };
            })()),
            t && "string" == typeof t
              ? (this._commitment = t)
              : t &&
                ((this._commitment = t.commitment),
                (this._confirmTransactionInitialTimeout =
                  t.confirmTransactionInitialTimeout),
                (i = t.wsEndpoint),
                (s = t.httpHeaders),
                (n = t.fetch),
                (o = t.fetchMiddleware),
                (a = t.disableRetryOnRateLimit),
                (c = t.httpAgent)),
            (this._rpcEndpoint = (function (e) {
              if (!1 === /^https?:/.test(e))
                throw TypeError(
                  "Endpoint URL must start with `http:` or `https:`."
                );
              return e;
            })(e)),
            (this._rpcWsEndpoint =
              i ||
              (function (e) {
                let t = e.match(eF);
                if (null == t)
                  throw TypeError(`Failed to validate endpoint URL \`${e}\``);
                let [r, i, s, n] = t,
                  o = e.startsWith("https:") ? "wss:" : "ws:",
                  a = null == s ? null : parseInt(s.slice(1), 10),
                  c = null == a ? "" : `:${a + 1}`;
                return `${o}//${i}${c}${n}`;
              })(e)),
            (this._rpcClient = (function (e, t, r, i, s, n) {
              let o,
                a,
                c = r || ej;
              return (
                null != n &&
                  console.warn(
                    "You have supplied an `httpAgent` when creating a `Connection` in a browser environment.It has been ignored; `httpAgent` is only used in Node environments."
                  ),
                i &&
                  (a = async (e, t) => {
                    let r = await new Promise((r, s) => {
                      try {
                        i(e, t, (e, t) => r([e, t]));
                      } catch (e) {
                        s(e);
                      }
                    });
                    return await c(...r);
                  }),
                new (y())(async (r, i) => {
                  let n = {
                    method: "POST",
                    body: r,
                    agent: o,
                    headers: Object.assign(
                      {
                        "Content-Type": "application/json",
                      },
                      t || {},
                      rf
                    ),
                  };
                  try {
                    let t,
                      r = 5,
                      o = 500;
                    for (
                      ;
                      ((t = a ? await a(e, n) : await c(e, n)),
                      429 === t.status && !0 !== s) && ((r -= 1), 0 !== r);

                    ) {
                      console.error(
                        `Server responded with ${t.status} ${t.statusText}.  Retrying after ${o}ms delay...`
                      ),
                        await ek(o),
                        (o *= 2);
                    }
                    let u = await t.text();
                    t.ok
                      ? i(null, u)
                      : i(Error(`${t.status} ${t.statusText}: ${u}`));
                  } catch (e) {
                    e instanceof Error && i(e);
                  }
                }, {})
              );
            })(e, s, n, o, a, c)),
            (this._rpcRequest =
              ((r = this._rpcClient),
              (e, t) =>
                new Promise((i, s) => {
                  r.request(e, t, (e, t) => {
                    if (e) return void s(e);
                    i(t);
                  });
                }))),
            (this._rpcBatchRequest = (function (e) {
              return (t) =>
                new Promise((r, i) => {
                  0 === t.length && r([]);
                  let s = t.map((t) => e.request(t.methodName, t.args));
                  e.request(s, (e, t) => {
                    if (e) return void i(e);
                    r(t);
                  });
                });
            })(this._rpcClient)),
            (this._rpcWebSocket = new eU(this._rpcWsEndpoint, {
              autoconnect: !1,
              max_reconnects: 1 / 0,
            })),
            this._rpcWebSocket.on("open", this._wsOnOpen.bind(this)),
            this._rpcWebSocket.on("error", this._wsOnError.bind(this)),
            this._rpcWebSocket.on("close", this._wsOnClose.bind(this)),
            this._rpcWebSocket.on(
              "accountNotification",
              this._wsOnAccountNotification.bind(this)
            ),
            this._rpcWebSocket.on(
              "programNotification",
              this._wsOnProgramAccountNotification.bind(this)
            ),
            this._rpcWebSocket.on(
              "slotNotification",
              this._wsOnSlotNotification.bind(this)
            ),
            this._rpcWebSocket.on(
              "slotsUpdatesNotification",
              this._wsOnSlotUpdatesNotification.bind(this)
            ),
            this._rpcWebSocket.on(
              "signatureNotification",
              this._wsOnSignatureNotification.bind(this)
            ),
            this._rpcWebSocket.on(
              "rootNotification",
              this._wsOnRootNotification.bind(this)
            ),
            this._rpcWebSocket.on(
              "logsNotification",
              this._wsOnLogsNotification.bind(this)
            );
        }
        get commitment() {
          return this._commitment;
        }
        get rpcEndpoint() {
          return this._rpcEndpoint;
        }
        async getBalanceAndContext(e, t) {
          let { commitment: r, config: i } = eZ(t),
            s = this._buildArgs([e.toBase58()], r, void 0, i),
            n = await this._rpcRequest("getBalance", s),
            o = (0, p.vt)(n, e3((0, p.ai)()));
          if ("error" in o)
            throw new eH(o.error, `failed to get balance for ${e.toBase58()}`);
          return o.result;
        }
        async getBalance(e, t) {
          return await this.getBalanceAndContext(e, t)
            .then((e) => e.value)
            .catch((t) => {
              throw Error(
                "failed to get balance of account " + e.toBase58() + ": " + t
              );
            });
        }
        async getBlockTime(e) {
          let t = await this._rpcRequest("getBlockTime", [e]),
            r = (0, p.vt)(t, e1((0, p.me)((0, p.ai)())));
          if ("error" in r)
            throw new eH(r.error, `failed to get block time for slot ${e}`);
          return r.result;
        }
        async getMinimumLedgerSlot() {
          let e = await this._rpcRequest("minimumLedgerSlot", []),
            t = (0, p.vt)(e, e1((0, p.ai)()));
          if ("error" in t)
            throw new eH(t.error, "failed to get minimum ledger slot");
          return t.result;
        }
        async getFirstAvailableBlock() {
          let e = await this._rpcRequest("getFirstAvailableBlock", []),
            t = (0, p.vt)(e, tp);
          if ("error" in t)
            throw new eH(t.error, "failed to get first available block");
          return t.result;
        }
        async getSupply(e) {
          let t = {};
          t =
            "string" == typeof e
              ? {
                  commitment: e,
                }
              : e
              ? {
                  ...e,
                  commitment: (e && e.commitment) || this.commitment,
                }
              : {
                  commitment: this.commitment,
                };
          let r = await this._rpcRequest("getSupply", [t]),
            i = (0, p.vt)(r, tm);
          if ("error" in i) throw new eH(i.error, "failed to get supply");
          return i.result;
        }
        async getTokenSupply(e, t) {
          let r = this._buildArgs([e.toBase58()], t),
            i = await this._rpcRequest("getTokenSupply", r),
            s = (0, p.vt)(i, e3(ty));
          if ("error" in s) throw new eH(s.error, "failed to get token supply");
          return s.result;
        }
        async getTokenAccountBalance(e, t) {
          let r = this._buildArgs([e.toBase58()], t),
            i = await this._rpcRequest("getTokenAccountBalance", r),
            s = (0, p.vt)(i, e3(ty));
          if ("error" in s)
            throw new eH(s.error, "failed to get token account balance");
          return s.result;
        }
        async getTokenAccountsByOwner(e, t, r) {
          let { commitment: i, config: s } = eZ(r),
            n = [e.toBase58()];
          "mint" in t
            ? n.push({
                mint: t.mint.toBase58(),
              })
            : n.push({
                programId: t.programId.toBase58(),
              });
          let o = this._buildArgs(n, i, "base64", s),
            a = await this._rpcRequest("getTokenAccountsByOwner", o),
            c = (0, p.vt)(a, tf);
          if ("error" in c)
            throw new eH(
              c.error,
              `failed to get token accounts owned by account ${e.toBase58()}`
            );
          return c.result;
        }
        async getParsedTokenAccountsByOwner(e, t, r) {
          let i = [e.toBase58()];
          "mint" in t
            ? i.push({
                mint: t.mint.toBase58(),
              })
            : i.push({
                programId: t.programId.toBase58(),
              });
          let s = this._buildArgs(i, r, "jsonParsed"),
            n = await this._rpcRequest("getTokenAccountsByOwner", s),
            o = (0, p.vt)(n, tw);
          if ("error" in o)
            throw new eH(
              o.error,
              `failed to get token accounts owned by account ${e.toBase58()}`
            );
          return o.result;
        }
        async getLargestAccounts(e) {
          let t = {
              ...e,
              commitment: (e && e.commitment) || this.commitment,
            },
            r = t.filter || t.commitment ? [t] : [],
            i = await this._rpcRequest("getLargestAccounts", r),
            s = (0, p.vt)(i, tS);
          if ("error" in s)
            throw new eH(s.error, "failed to get largest accounts");
          return s.result;
        }
        async getTokenLargestAccounts(e, t) {
          let r = this._buildArgs([e.toBase58()], t),
            i = await this._rpcRequest("getTokenLargestAccounts", r),
            s = (0, p.vt)(i, tb);
          if ("error" in s)
            throw new eH(s.error, "failed to get token largest accounts");
          return s.result;
        }
        async getAccountInfoAndContext(e, t) {
          let { commitment: r, config: i } = eZ(t),
            s = this._buildArgs([e.toBase58()], r, "base64", i),
            n = await this._rpcRequest("getAccountInfo", s),
            o = (0, p.vt)(n, e3((0, p.me)(tI)));
          if ("error" in o)
            throw new eH(
              o.error,
              `failed to get info about account ${e.toBase58()}`
            );
          return o.result;
        }
        async getParsedAccountInfo(e, t) {
          let { commitment: r, config: i } = eZ(t),
            s = this._buildArgs([e.toBase58()], r, "jsonParsed", i),
            n = await this._rpcRequest("getAccountInfo", s),
            o = (0, p.vt)(n, e3((0, p.me)(tA)));
          if ("error" in o)
            throw new eH(
              o.error,
              `failed to get info about account ${e.toBase58()}`
            );
          return o.result;
        }
        async getAccountInfo(e, t) {
          try {
            return (await this.getAccountInfoAndContext(e, t)).value;
          } catch (t) {
            throw Error(
              "failed to get info about account " + e.toBase58() + ": " + t
            );
          }
        }
        async getMultipleParsedAccounts(e, t) {
          let { commitment: r, config: i } = eZ(t),
            s = e.map((e) => e.toBase58()),
            n = this._buildArgs([s], r, "jsonParsed", i),
            o = await this._rpcRequest("getMultipleAccounts", n),
            a = (0, p.vt)(o, e3((0, p.YO)((0, p.me)(tA))));
          if ("error" in a)
            throw new eH(a.error, `failed to get info for accounts ${s}`);
          return a.result;
        }
        async getMultipleAccountsInfoAndContext(e, t) {
          let { commitment: r, config: i } = eZ(t),
            s = e.map((e) => e.toBase58()),
            n = this._buildArgs([s], r, "base64", i),
            o = await this._rpcRequest("getMultipleAccounts", n),
            a = (0, p.vt)(o, e3((0, p.YO)((0, p.me)(tI))));
          if ("error" in a)
            throw new eH(a.error, `failed to get info for accounts ${s}`);
          return a.result;
        }
        async getMultipleAccountsInfo(e, t) {
          return (await this.getMultipleAccountsInfoAndContext(e, t)).value;
        }
        async getStakeActivation(e, t, r) {
          let { commitment: i, config: s } = eZ(t),
            n = this._buildArgs([e.toBase58()], i, void 0, {
              ...s,
              epoch: null != r ? r : s?.epoch,
            }),
            o = await this._rpcRequest("getStakeActivation", n),
            a = (0, p.vt)(o, e1(tE));
          if ("error" in a)
            throw new eH(
              a.error,
              `failed to get Stake Activation ${e.toBase58()}`
            );
          return a.result;
        }
        async getProgramAccounts(e, t) {
          let { commitment: r, config: i } = eZ(t),
            { encoding: s, ...n } = i || {},
            o = this._buildArgs([e.toBase58()], r, s || "base64", n),
            a = await this._rpcRequest("getProgramAccounts", o),
            c = (0, p.YO)(t_),
            u =
              !0 === n.withContext ? (0, p.vt)(a, e3(c)) : (0, p.vt)(a, e1(c));
          if ("error" in u)
            throw new eH(
              u.error,
              `failed to get accounts owned by program ${e.toBase58()}`
            );
          return u.result;
        }
        async getParsedProgramAccounts(e, t) {
          let { commitment: r, config: i } = eZ(t),
            s = this._buildArgs([e.toBase58()], r, "jsonParsed", i),
            n = await this._rpcRequest("getProgramAccounts", s),
            o = (0, p.vt)(n, e1((0, p.YO)(tP)));
          if ("error" in o)
            throw new eH(
              o.error,
              `failed to get accounts owned by program ${e.toBase58()}`
            );
          return o.result;
        }
        async confirmTransaction(e, t) {
          let r, i;
          if ("string" == typeof e) r = e;
          else {
            if (e.abortSignal?.aborted)
              return Promise.reject(e.abortSignal.reason);
            r = e.signature;
          }
          try {
            i = u().decode(r);
          } catch (e) {
            throw Error("signature must be base58 encoded: " + r);
          }
          return (J(64 === i.length, "signature has invalid length"),
          "string" == typeof e)
            ? await this.confirmTransactionUsingLegacyTimeoutStrategy({
                commitment: t || this.commitment,
                signature: r,
              })
            : "lastValidBlockHeight" in e
            ? await this.confirmTransactionUsingBlockHeightExceedanceStrategy({
                commitment: t || this.commitment,
                strategy: e,
              })
            : await this.confirmTransactionUsingDurableNonceStrategy({
                commitment: t || this.commitment,
                strategy: e,
              });
        }
        getCancellationPromise(e) {
          return new Promise((t, r) => {
            null != e &&
              (e.aborted
                ? r(e.reason)
                : e.addEventListener("abort", () => {
                    r(e.reason);
                  }));
          });
        }
        getTransactionConfirmationPromise({ commitment: e, signature: t }) {
          let r,
            i,
            s = !1;
          return {
            abortConfirmation: () => {
              i && (i(), (i = void 0)),
                null != r && (this.removeSignatureListener(r), (r = void 0));
            },
            confirmationPromise: new Promise((n, o) => {
              try {
                r = this.onSignature(
                  t,
                  (e, t) => {
                    (r = void 0),
                      n({
                        __type: ei.PROCESSED,
                        response: {
                          context: t,
                          value: e,
                        },
                      });
                  },
                  e
                );
                let a = new Promise((e) => {
                  null == r
                    ? e()
                    : (i = this._onSubscriptionStateChange(r, (t) => {
                        "subscribed" === t && e();
                      }));
                });
                (async () => {
                  if ((await a, s)) return;
                  let r = await this.getSignatureStatus(t);
                  if (s || null == r) return;
                  let { context: i, value: c } = r;
                  if (null != c)
                    if (c?.err) o(c.err);
                    else {
                      switch (e) {
                        case "confirmed":
                        case "single":
                        case "singleGossip":
                          if ("processed" === c.confirmationStatus) return;
                          break;
                        case "finalized":
                        case "max":
                        case "root":
                          if (
                            "processed" === c.confirmationStatus ||
                            "confirmed" === c.confirmationStatus
                          )
                            return;
                      }
                      (s = !0),
                        n({
                          __type: ei.PROCESSED,
                          response: {
                            context: i,
                            value: c,
                          },
                        });
                    }
                })();
              } catch (e) {
                o(e);
              }
            }),
          };
        }
        async confirmTransactionUsingBlockHeightExceedanceStrategy({
          commitment: e,
          strategy: { abortSignal: t, lastValidBlockHeight: r, signature: i },
        }) {
          let s,
            n = !1,
            o = new Promise((t) => {
              let i = async () => {
                try {
                  return await this.getBlockHeight(e);
                } catch (e) {
                  return -1;
                }
              };
              (async () => {
                let e = await i();
                if (!n) {
                  for (; e <= r; )
                    if ((await ek(1e3), n || ((e = await i()), n))) return;
                  t({
                    __type: ei.BLOCKHEIGHT_EXCEEDED,
                  });
                }
              })();
            }),
            { abortConfirmation: a, confirmationPromise: c } =
              this.getTransactionConfirmationPromise({
                commitment: e,
                signature: i,
              }),
            u = this.getCancellationPromise(t);
          try {
            let e = await Promise.race([u, c, o]);
            if (e.__type === ei.PROCESSED) s = e.response;
            else throw new q(i);
          } finally {
            (n = !0), a();
          }
          return s;
        }
        async confirmTransactionUsingDurableNonceStrategy({
          commitment: e,
          strategy: {
            abortSignal: t,
            minContextSlot: r,
            nonceAccountPubkey: i,
            nonceValue: s,
            signature: n,
          },
        }) {
          let o,
            a = !1,
            c = new Promise((t) => {
              let n = s,
                o = null,
                c = async () => {
                  try {
                    let { context: t, value: s } =
                      await this.getNonceAndContext(i, {
                        commitment: e,
                        minContextSlot: r,
                      });
                    return (o = t.slot), s?.nonce;
                  } catch (e) {
                    return n;
                  }
                };
              (async () => {
                if (((n = await c()), !a))
                  for (;;) {
                    if (s !== n)
                      return void t({
                        __type: ei.NONCE_INVALID,
                        slotInWhichNonceDidAdvance: o,
                      });
                    if ((await ek(2e3), a || ((n = await c()), a))) return;
                  }
              })();
            }),
            { abortConfirmation: u, confirmationPromise: l } =
              this.getTransactionConfirmationPromise({
                commitment: e,
                signature: n,
              }),
            d = this.getCancellationPromise(t);
          try {
            let t = await Promise.race([d, l, c]);
            if (t.__type === ei.PROCESSED) o = t.response;
            else {
              let i;
              for (;;) {
                let e = await this.getSignatureStatus(n);
                if (null == e) break;
                if (e.context.slot < (t.slotInWhichNonceDidAdvance ?? r)) {
                  await ek(400);
                  continue;
                }
                i = e;
                break;
              }
              if (i?.value) {
                let t = e || "finalized",
                  { confirmationStatus: r } = i.value;
                switch (t) {
                  case "processed":
                  case "recent":
                    if (
                      "processed" !== r &&
                      "confirmed" !== r &&
                      "finalized" !== r
                    )
                      throw new H(n);
                    break;
                  case "confirmed":
                  case "single":
                  case "singleGossip":
                    if ("confirmed" !== r && "finalized" !== r) throw new H(n);
                    break;
                  case "finalized":
                  case "max":
                  case "root":
                    if ("finalized" !== r) throw new H(n);
                }
                o = {
                  context: i.context,
                  value: {
                    err: i.value.err,
                  },
                };
              } else throw new H(n);
            }
          } finally {
            (a = !0), u();
          }
          return o;
        }
        async confirmTransactionUsingLegacyTimeoutStrategy({
          commitment: e,
          signature: t,
        }) {
          let r,
            i,
            s = new Promise((t) => {
              let i = this._confirmTransactionInitialTimeout || 6e4;
              switch (e) {
                case "processed":
                case "recent":
                case "single":
                case "confirmed":
                case "singleGossip":
                  i = this._confirmTransactionInitialTimeout || 3e4;
              }
              r = setTimeout(
                () =>
                  t({
                    __type: ei.TIMED_OUT,
                    timeoutMs: i,
                  }),
                i
              );
            }),
            { abortConfirmation: n, confirmationPromise: o } =
              this.getTransactionConfirmationPromise({
                commitment: e,
                signature: t,
              });
          try {
            let e = await Promise.race([o, s]);
            if (e.__type === ei.PROCESSED) i = e.response;
            else throw new D(t, e.timeoutMs / 1e3);
          } finally {
            clearTimeout(r), n();
          }
          return i;
        }
        async getClusterNodes() {
          let e = await this._rpcRequest("getClusterNodes", []),
            t = (0, p.vt)(e, e1((0, p.YO)(tY)));
          if ("error" in t)
            throw new eH(t.error, "failed to get cluster nodes");
          return t.result;
        }
        async getVoteAccounts(e) {
          let t = this._buildArgs([], e),
            r = await this._rpcRequest("getVoteAccounts", t),
            i = (0, p.vt)(r, tD);
          if ("error" in i)
            throw new eH(i.error, "failed to get vote accounts");
          return i.result;
        }
        async getSlot(e) {
          let { commitment: t, config: r } = eZ(e),
            i = this._buildArgs([], t, void 0, r),
            s = await this._rpcRequest("getSlot", i),
            n = (0, p.vt)(s, e1((0, p.ai)()));
          if ("error" in n) throw new eH(n.error, "failed to get slot");
          return n.result;
        }
        async getSlotLeader(e) {
          let { commitment: t, config: r } = eZ(e),
            i = this._buildArgs([], t, void 0, r),
            s = await this._rpcRequest("getSlotLeader", i),
            n = (0, p.vt)(s, e1((0, p.Yj)()));
          if ("error" in n) throw new eH(n.error, "failed to get slot leader");
          return n.result;
        }
        async getSlotLeaders(e, t) {
          let r = await this._rpcRequest("getSlotLeaders", [e, t]),
            i = (0, p.vt)(r, e1((0, p.YO)(e$)));
          if ("error" in i) throw new eH(i.error, "failed to get slot leaders");
          return i.result;
        }
        async getSignatureStatus(e, t) {
          let { context: r, value: i } = await this.getSignatureStatuses(
            [e],
            t
          );
          return (
            J(1 === i.length),
            {
              context: r,
              value: i[0],
            }
          );
        }
        async getSignatureStatuses(e, t) {
          let r = [e];
          t && r.push(t);
          let i = await this._rpcRequest("getSignatureStatuses", r),
            s = (0, p.vt)(i, tU);
          if ("error" in s)
            throw new eH(s.error, "failed to get signature status");
          return s.result;
        }
        async getTransactionCount(e) {
          let { commitment: t, config: r } = eZ(e),
            i = this._buildArgs([], t, void 0, r),
            s = await this._rpcRequest("getTransactionCount", i),
            n = (0, p.vt)(s, e1((0, p.ai)()));
          if ("error" in n)
            throw new eH(n.error, "failed to get transaction count");
          return n.result;
        }
        async getTotalSupply(e) {
          return (
            await this.getSupply({
              commitment: e,
              excludeNonCirculatingAccountsList: !0,
            })
          ).value.total;
        }
        async getInflationGovernor(e) {
          let t = this._buildArgs([], e),
            r = await this._rpcRequest("getInflationGovernor", t),
            i = (0, p.vt)(r, tc);
          if ("error" in i) throw new eH(i.error, "failed to get inflation");
          return i.result;
        }
        async getInflationReward(e, t, r) {
          let { commitment: i, config: s } = eZ(r),
            n = this._buildArgs([e.map((e) => e.toBase58())], i, void 0, {
              ...s,
              epoch: null != t ? t : s?.epoch,
            }),
            o = await this._rpcRequest("getInflationReward", n),
            a = (0, p.vt)(o, e6);
          if ("error" in a)
            throw new eH(a.error, "failed to get inflation reward");
          return a.result;
        }
        async getInflationRate() {
          let e = await this._rpcRequest("getInflationRate", []),
            t = (0, p.vt)(e, tu);
          if ("error" in t)
            throw new eH(t.error, "failed to get inflation rate");
          return t.result;
        }
        async getEpochInfo(e) {
          let { commitment: t, config: r } = eZ(e),
            i = this._buildArgs([], t, void 0, r),
            s = await this._rpcRequest("getEpochInfo", i),
            n = (0, p.vt)(s, td);
          if ("error" in n) throw new eH(n.error, "failed to get epoch info");
          return n.result;
        }
        async getEpochSchedule() {
          let e = await this._rpcRequest("getEpochSchedule", []),
            t = (0, p.vt)(e, th);
          if ("error" in t)
            throw new eH(t.error, "failed to get epoch schedule");
          let r = t.result;
          return new eY(
            r.slotsPerEpoch,
            r.leaderScheduleSlotOffset,
            r.warmup,
            r.firstNormalEpoch,
            r.firstNormalSlot
          );
        }
        async getLeaderSchedule() {
          let e = await this._rpcRequest("getLeaderSchedule", []),
            t = (0, p.vt)(e, tg);
          if ("error" in t)
            throw new eH(t.error, "failed to get leader schedule");
          return t.result;
        }
        async getMinimumBalanceForRentExemption(e, t) {
          let r = this._buildArgs([e], t),
            i = await this._rpcRequest("getMinimumBalanceForRentExemption", r),
            s = (0, p.vt)(i, tM);
          return "error" in s
            ? (console.warn(
                "Unable to fetch minimum balance for rent exemption"
              ),
              0)
            : s.result;
        }
        async getRecentBlockhashAndContext(e) {
          let t = this._buildArgs([], e),
            r = await this._rpcRequest("getRecentBlockhash", t),
            i = (0, p.vt)(r, rc);
          if ("error" in i)
            throw new eH(i.error, "failed to get recent blockhash");
          return i.result;
        }
        async getRecentPerformanceSamples(e) {
          let t = await this._rpcRequest(
              "getRecentPerformanceSamples",
              e ? [e] : []
            ),
            r = (0, p.vt)(t, rh);
          if ("error" in r)
            throw new eH(r.error, "failed to get recent performance samples");
          return r.result;
        }
        async getFeeCalculatorForBlockhash(e, t) {
          let r = this._buildArgs([e], t),
            i = await this._rpcRequest("getFeeCalculatorForBlockhash", r),
            s = (0, p.vt)(i, rg);
          if ("error" in s)
            throw new eH(s.error, "failed to get fee calculator");
          let { context: n, value: o } = s.result;
          return {
            context: n,
            value: null !== o ? o.feeCalculator : null,
          };
        }
        async getFeeForMessage(e, t) {
          let r = E(e.serialize()).toString("base64"),
            i = this._buildArgs([r], t),
            s = await this._rpcRequest("getFeeForMessage", i),
            n = (0, p.vt)(s, e3((0, p.me)((0, p.ai)())));
          if ("error" in n)
            throw new eH(n.error, "failed to get fee for message");
          if (null === n.result) throw Error("invalid blockhash");
          return n.result;
        }
        async getRecentPrioritizationFees(e) {
          let t = e?.lockedWritableAccounts?.map((e) => e.toBase58()),
            r = t?.length ? [t] : [],
            i = await this._rpcRequest("getRecentPrioritizationFees", r),
            s = (0, p.vt)(i, tl);
          if ("error" in s)
            throw new eH(s.error, "failed to get recent prioritization fees");
          return s.result;
        }
        async getRecentBlockhash(e) {
          try {
            return (await this.getRecentBlockhashAndContext(e)).value;
          } catch (e) {
            throw Error("failed to get recent blockhash: " + e);
          }
        }
        async getLatestBlockhash(e) {
          try {
            return (await this.getLatestBlockhashAndContext(e)).value;
          } catch (e) {
            throw Error("failed to get recent blockhash: " + e);
          }
        }
        async getLatestBlockhashAndContext(e) {
          let { commitment: t, config: r } = eZ(e),
            i = this._buildArgs([], t, void 0, r),
            s = await this._rpcRequest("getLatestBlockhash", i),
            n = (0, p.vt)(s, ru);
          if ("error" in n)
            throw new eH(n.error, "failed to get latest blockhash");
          return n.result;
        }
        async isBlockhashValid(e, t) {
          let { commitment: r, config: i } = eZ(t),
            s = this._buildArgs([e], r, void 0, i),
            n = await this._rpcRequest("isBlockhashValid", s),
            o = (0, p.vt)(n, rl);
          if ("error" in o)
            throw new eH(
              o.error,
              "failed to determine if the blockhash `" + e + "`is valid"
            );
          return o.result;
        }
        async getVersion() {
          let e = await this._rpcRequest("getVersion", []),
            t = (0, p.vt)(e, e1(tn));
          if ("error" in t) throw new eH(t.error, "failed to get version");
          return t.result;
        }
        async getGenesisHash() {
          let e = await this._rpcRequest("getGenesisHash", []),
            t = (0, p.vt)(e, e1((0, p.Yj)()));
          if ("error" in t) throw new eH(t.error, "failed to get genesis hash");
          return t.result;
        }
        async getBlock(e, t) {
          let { commitment: r, config: i } = eZ(t),
            s = this._buildArgsAtLeastConfirmed([e], r, void 0, i),
            n = await this._rpcRequest("getBlock", s);
          try {
            switch (i?.transactionDetails) {
              case "accounts": {
                let e = (0, p.vt)(n, re);
                if ("error" in e) throw e.error;
                return e.result;
              }
              case "none": {
                let e = (0, p.vt)(n, t9);
                if ("error" in e) throw e.error;
                return e.result;
              }
              default: {
                let e = (0, p.vt)(n, t7);
                if ("error" in e) throw e.error;
                let { result: t } = e;
                return t
                  ? {
                      ...t,
                      transactions: t.transactions.map(
                        ({ transaction: e, meta: t, version: r }) => ({
                          meta: t,
                          transaction: {
                            ...e,
                            message: e2(r, e.message),
                          },
                          version: r,
                        })
                      ),
                    }
                  : null;
              }
            }
          } catch (e) {
            throw new eH(e, "failed to get confirmed block");
          }
        }
        async getParsedBlock(e, t) {
          let { commitment: r, config: i } = eZ(t),
            s = this._buildArgsAtLeastConfirmed([e], r, "jsonParsed", i),
            n = await this._rpcRequest("getBlock", s);
          try {
            switch (i?.transactionDetails) {
              case "accounts": {
                let e = (0, p.vt)(n, rr);
                if ("error" in e) throw e.error;
                return e.result;
              }
              case "none": {
                let e = (0, p.vt)(n, ri);
                if ("error" in e) throw e.error;
                return e.result;
              }
              default: {
                let e = (0, p.vt)(n, rt);
                if ("error" in e) throw e.error;
                return e.result;
              }
            }
          } catch (e) {
            throw new eH(e, "failed to get block");
          }
        }
        async getBlockProduction(e) {
          let t, r;
          if ("string" == typeof e) r = e;
          else if (e) {
            let { commitment: i, ...s } = e;
            (r = i), (t = s);
          }
          let i = this._buildArgs([], r, "base64", t),
            s = await this._rpcRequest("getBlockProduction", i),
            n = (0, p.vt)(s, ta);
          if ("error" in n)
            throw new eH(n.error, "failed to get block production information");
          return n.result;
        }
        async getTransaction(e, t) {
          let { commitment: r, config: i } = eZ(t),
            s = this._buildArgsAtLeastConfirmed([e], r, void 0, i),
            n = await this._rpcRequest("getTransaction", s),
            o = (0, p.vt)(n, ro);
          if ("error" in o) throw new eH(o.error, "failed to get transaction");
          let a = o.result;
          return a
            ? {
                ...a,
                transaction: {
                  ...a.transaction,
                  message: e2(a.version, a.transaction.message),
                },
              }
            : a;
        }
        async getParsedTransaction(e, t) {
          let { commitment: r, config: i } = eZ(t),
            s = this._buildArgsAtLeastConfirmed([e], r, "jsonParsed", i),
            n = await this._rpcRequest("getTransaction", s),
            o = (0, p.vt)(n, ra);
          if ("error" in o) throw new eH(o.error, "failed to get transaction");
          return o.result;
        }
        async getParsedTransactions(e, t) {
          let { commitment: r, config: i } = eZ(t),
            s = e.map((e) => ({
              methodName: "getTransaction",
              args: this._buildArgsAtLeastConfirmed([e], r, "jsonParsed", i),
            }));
          return (await this._rpcBatchRequest(s)).map((e) => {
            let t = (0, p.vt)(e, ra);
            if ("error" in t)
              throw new eH(t.error, "failed to get transactions");
            return t.result;
          });
        }
        async getTransactions(e, t) {
          let { commitment: r, config: i } = eZ(t),
            s = e.map((e) => ({
              methodName: "getTransaction",
              args: this._buildArgsAtLeastConfirmed([e], r, void 0, i),
            }));
          return (await this._rpcBatchRequest(s)).map((e) => {
            let t = (0, p.vt)(e, ro);
            if ("error" in t)
              throw new eH(t.error, "failed to get transactions");
            let r = t.result;
            return r
              ? {
                  ...r,
                  transaction: {
                    ...r.transaction,
                    message: e2(r.version, r.transaction.message),
                  },
                }
              : r;
          });
        }
        async getConfirmedBlock(e, t) {
          let r = this._buildArgsAtLeastConfirmed([e], t),
            i = await this._rpcRequest("getConfirmedBlock", r),
            s = (0, p.vt)(i, rs);
          if ("error" in s)
            throw new eH(s.error, "failed to get confirmed block");
          let n = s.result;
          if (!n) throw Error("Confirmed block " + e + " not found");
          let o = {
            ...n,
            transactions: n.transactions.map(({ transaction: e, meta: t }) => {
              let r = new ee(e.message);
              return {
                meta: t,
                transaction: {
                  ...e,
                  message: r,
                },
              };
            }),
          };
          return {
            ...o,
            transactions: o.transactions.map(({ transaction: e, meta: t }) => ({
              meta: t,
              transaction: eo.populate(e.message, e.signatures),
            })),
          };
        }
        async getBlocks(e, t, r) {
          let i = this._buildArgsAtLeastConfirmed(
              void 0 !== t ? [e, t] : [e],
              r
            ),
            s = await this._rpcRequest("getBlocks", i),
            n = (0, p.vt)(s, e1((0, p.YO)((0, p.ai)())));
          if ("error" in n) throw new eH(n.error, "failed to get blocks");
          return n.result;
        }
        async getBlockSignatures(e, t) {
          let r = this._buildArgsAtLeastConfirmed([e], t, void 0, {
              transactionDetails: "signatures",
              rewards: !1,
            }),
            i = await this._rpcRequest("getBlock", r),
            s = (0, p.vt)(i, rn);
          if ("error" in s) throw new eH(s.error, "failed to get block");
          let n = s.result;
          if (!n) throw Error("Block " + e + " not found");
          return n;
        }
        async getConfirmedBlockSignatures(e, t) {
          let r = this._buildArgsAtLeastConfirmed([e], t, void 0, {
              transactionDetails: "signatures",
              rewards: !1,
            }),
            i = await this._rpcRequest("getConfirmedBlock", r),
            s = (0, p.vt)(i, rn);
          if ("error" in s)
            throw new eH(s.error, "failed to get confirmed block");
          let n = s.result;
          if (!n) throw Error("Confirmed block " + e + " not found");
          return n;
        }
        async getConfirmedTransaction(e, t) {
          let r = this._buildArgsAtLeastConfirmed([e], t),
            i = await this._rpcRequest("getConfirmedTransaction", r),
            s = (0, p.vt)(i, ro);
          if ("error" in s) throw new eH(s.error, "failed to get transaction");
          let n = s.result;
          if (!n) return n;
          let o = new ee(n.transaction.message),
            a = n.transaction.signatures;
          return {
            ...n,
            transaction: eo.populate(o, a),
          };
        }
        async getParsedConfirmedTransaction(e, t) {
          let r = this._buildArgsAtLeastConfirmed([e], t, "jsonParsed"),
            i = await this._rpcRequest("getConfirmedTransaction", r),
            s = (0, p.vt)(i, ra);
          if ("error" in s)
            throw new eH(s.error, "failed to get confirmed transaction");
          return s.result;
        }
        async getParsedConfirmedTransactions(e, t) {
          let r = e.map((e) => ({
            methodName: "getConfirmedTransaction",
            args: this._buildArgsAtLeastConfirmed([e], t, "jsonParsed"),
          }));
          return (await this._rpcBatchRequest(r)).map((e) => {
            let t = (0, p.vt)(e, ra);
            if ("error" in t)
              throw new eH(t.error, "failed to get confirmed transactions");
            return t.result;
          });
        }
        async getConfirmedSignaturesForAddress(e, t, r) {
          let i = {},
            s = await this.getFirstAvailableBlock();
          for (; !("until" in i) && !(--t <= 0) && !(t < s); )
            try {
              let e = await this.getConfirmedBlockSignatures(t, "finalized");
              e.signatures.length > 0 &&
                (i.until = e.signatures[e.signatures.length - 1].toString());
            } catch (e) {
              if (e instanceof Error && e.message.includes("skipped")) continue;
              throw e;
            }
          let n = await this.getSlot("finalized");
          for (; !("before" in i) && !(++r > n); )
            try {
              let e = await this.getConfirmedBlockSignatures(r);
              e.signatures.length > 0 &&
                (i.before = e.signatures[e.signatures.length - 1].toString());
            } catch (e) {
              if (e instanceof Error && e.message.includes("skipped")) continue;
              throw e;
            }
          return (await this.getConfirmedSignaturesForAddress2(e, i)).map(
            (e) => e.signature
          );
        }
        async getConfirmedSignaturesForAddress2(e, t, r) {
          let i = this._buildArgsAtLeastConfirmed([e.toBase58()], r, void 0, t),
            s = await this._rpcRequest("getConfirmedSignaturesForAddress2", i),
            n = (0, p.vt)(s, tB);
          if ("error" in n)
            throw new eH(
              n.error,
              "failed to get confirmed signatures for address"
            );
          return n.result;
        }
        async getSignaturesForAddress(e, t, r) {
          let i = this._buildArgsAtLeastConfirmed([e.toBase58()], r, void 0, t),
            s = await this._rpcRequest("getSignaturesForAddress", i),
            n = (0, p.vt)(s, tW);
          if ("error" in n)
            throw new eH(n.error, "failed to get signatures for address");
          return n.result;
        }
        async getAddressLookupTable(e, t) {
          let { context: r, value: i } = await this.getAccountInfoAndContext(
              e,
              t
            ),
            s = null;
          return (
            null !== i &&
              (s = new eM({
                key: e,
                state: eM.deserialize(i.data),
              })),
            {
              context: r,
              value: s,
            }
          );
        }
        async getNonceAndContext(e, t) {
          let { context: r, value: i } = await this.getAccountInfoAndContext(
              e,
              t
            ),
            s = null;
          return (
            null !== i && (s = eA.fromAccountData(i.data)),
            {
              context: r,
              value: s,
            }
          );
        }
        async getNonce(e, t) {
          return await this.getNonceAndContext(e, t)
            .then((e) => e.value)
            .catch((t) => {
              throw Error(
                "failed to get nonce for account " + e.toBase58() + ": " + t
              );
            });
        }
        async requestAirdrop(e, t) {
          let r = await this._rpcRequest("requestAirdrop", [e.toBase58(), t]),
            i = (0, p.vt)(r, rp);
          if ("error" in i)
            throw new eH(i.error, `airdrop to ${e.toBase58()} failed`);
          return i.result;
        }
        async _blockhashWithExpiryBlockHeight(e) {
          if (!e) {
            for (; this._pollingBlockhash; ) await ek(100);
            let e = Date.now() - this._blockhashInfo.lastFetch;
            if (null !== this._blockhashInfo.latestBlockhash && !(e >= eX))
              return this._blockhashInfo.latestBlockhash;
          }
          return await this._pollNewBlockhash();
        }
        async _pollNewBlockhash() {
          this._pollingBlockhash = !0;
          try {
            let e = Date.now(),
              t = this._blockhashInfo.latestBlockhash,
              r = t ? t.blockhash : null;
            for (let e = 0; e < 50; e++) {
              let e = await this.getLatestBlockhash("finalized");
              if (r !== e.blockhash)
                return (
                  (this._blockhashInfo = {
                    latestBlockhash: e,
                    lastFetch: Date.now(),
                    transactionSignatures: [],
                    simulatedSignatures: [],
                  }),
                  e
                );
              await ek(200);
            }
            throw Error(
              `Unable to obtain a new blockhash after ${Date.now() - e}ms`
            );
          } finally {
            this._pollingBlockhash = !1;
          }
        }
        async getStakeMinimumDelegation(e) {
          let { commitment: t, config: r } = eZ(e),
            i = this._buildArgs([], t, "base64", r),
            s = await this._rpcRequest("getStakeMinimumDelegation", i),
            n = (0, p.vt)(s, e3((0, p.ai)()));
          if ("error" in n)
            throw new eH(n.error, "failed to get stake minimum delegation");
          return n.result;
        }
        async simulateTransaction(e, t, r) {
          let i;
          if ("message" in e) {
            let i = e.serialize(),
              n = s.Buffer.from(i).toString("base64");
            if (Array.isArray(t) || void 0 !== r)
              throw Error("Invalid arguments");
            let o = t || {};
            (o.encoding = "base64"),
              "commitment" in o || (o.commitment = this.commitment);
            let a = [n, o],
              c = await this._rpcRequest("simulateTransaction", a),
              u = (0, p.vt)(c, to);
            if ("error" in u)
              throw Error("failed to simulate transaction: " + u.error.message);
            return u.result;
          }
          if (
            (e instanceof eo
              ? (((i = new eo()).feePayer = e.feePayer),
                (i.instructions = e.instructions),
                (i.nonceInfo = e.nonceInfo),
                (i.signatures = e.signatures))
              : ((i = eo.populate(e))._message = i._json = void 0),
            void 0 !== t && !Array.isArray(t))
          )
            throw Error("Invalid arguments");
          if (i.nonceInfo && t) i.sign(...t);
          else {
            let e = this._disableBlockhashCaching;
            for (;;) {
              let r = await this._blockhashWithExpiryBlockHeight(e);
              if (
                ((i.lastValidBlockHeight = r.lastValidBlockHeight),
                (i.recentBlockhash = r.blockhash),
                !t)
              )
                break;
              if ((i.sign(...t), !i.signature)) throw Error("!signature");
              let s = i.signature.toString("base64");
              if (
                this._blockhashInfo.simulatedSignatures.includes(s) ||
                this._blockhashInfo.transactionSignatures.includes(s)
              )
                e = !0;
              else {
                this._blockhashInfo.simulatedSignatures.push(s);
                break;
              }
            }
          }
          let n = i._compile(),
            o = n.serialize(),
            a = i._serialize(o).toString("base64"),
            c = {
              encoding: "base64",
              commitment: this.commitment,
            };
          r &&
            (c.accounts = {
              encoding: "base64",
              addresses: (Array.isArray(r) ? r : n.nonProgramIds()).map((e) =>
                e.toBase58()
              ),
            }),
            t && (c.sigVerify = !0);
          let u = [a, c],
            l = await this._rpcRequest("simulateTransaction", u),
            d = (0, p.vt)(l, to);
          if ("error" in d) {
            let e;
            if (
              "data" in d.error &&
              (e = d.error.data.logs) &&
              Array.isArray(e)
            ) {
              let t = "\n    ",
                r = t + e.join(t);
              console.error(d.error.message, r);
            }
            throw new eq(
              "failed to simulate transaction: " + d.error.message,
              e
            );
          }
          return d.result;
        }
        async sendTransaction(e, t, r) {
          if ("version" in e) {
            if (t && Array.isArray(t)) throw Error("Invalid arguments");
            let r = e.serialize();
            return await this.sendRawTransaction(r, t);
          }
          if (void 0 === t || !Array.isArray(t))
            throw Error("Invalid arguments");
          if (e.nonceInfo) e.sign(...t);
          else {
            let r = this._disableBlockhashCaching;
            for (;;) {
              let i = await this._blockhashWithExpiryBlockHeight(r);
              if (
                ((e.lastValidBlockHeight = i.lastValidBlockHeight),
                (e.recentBlockhash = i.blockhash),
                e.sign(...t),
                !e.signature)
              )
                throw Error("!signature");
              let s = e.signature.toString("base64");
              if (this._blockhashInfo.transactionSignatures.includes(s)) r = !0;
              else {
                this._blockhashInfo.transactionSignatures.push(s);
                break;
              }
            }
          }
          let i = e.serialize();
          return await this.sendRawTransaction(i, r);
        }
        async sendRawTransaction(e, t) {
          let r = E(e).toString("base64");
          return await this.sendEncodedTransaction(r, t);
        }
        async sendEncodedTransaction(e, t) {
          let r = {
              encoding: "base64",
            },
            i = t && t.skipPreflight,
            s =
              !0 === i
                ? "processed"
                : (t && t.preflightCommitment) || this.commitment;
          t && null != t.maxRetries && (r.maxRetries = t.maxRetries),
            t &&
              null != t.minContextSlot &&
              (r.minContextSlot = t.minContextSlot),
            i && (r.skipPreflight = i),
            s && (r.preflightCommitment = s);
          let n = [e, r],
            o = await this._rpcRequest("sendTransaction", n),
            a = (0, p.vt)(o, rm);
          if ("error" in a) {
            let e;
            throw (
              ("data" in a.error && (e = a.error.data.logs),
              new eq("failed to send transaction: " + a.error.message, e))
            );
          }
          return a.result;
        }
        _wsOnOpen() {
          (this._rpcWebSocketConnected = !0),
            (this._rpcWebSocketHeartbeat = setInterval(() => {
              (async () => {
                try {
                  await this._rpcWebSocket.notify("ping");
                } catch {}
              })();
            }, 5e3)),
            this._updateSubscriptions();
        }
        _wsOnError(e) {
          (this._rpcWebSocketConnected = !1),
            console.error("ws error:", e.message);
        }
        _wsOnClose(e) {
          if (
            ((this._rpcWebSocketConnected = !1),
            (this._rpcWebSocketGeneration =
              (this._rpcWebSocketGeneration + 1) % Number.MAX_SAFE_INTEGER),
            this._rpcWebSocketIdleTimeout &&
              (clearTimeout(this._rpcWebSocketIdleTimeout),
              (this._rpcWebSocketIdleTimeout = null)),
            this._rpcWebSocketHeartbeat &&
              (clearInterval(this._rpcWebSocketHeartbeat),
              (this._rpcWebSocketHeartbeat = null)),
            1e3 === e)
          )
            return void this._updateSubscriptions();
          (this._subscriptionCallbacksByServerSubscriptionId = {}),
            Object.entries(this._subscriptionsByHash).forEach(([e, t]) => {
              this._setSubscription(e, {
                ...t,
                state: "pending",
              });
            });
        }
        _setSubscription(e, t) {
          let r = this._subscriptionsByHash[e]?.state;
          if (((this._subscriptionsByHash[e] = t), r !== t.state)) {
            let r = this._subscriptionStateChangeCallbacksByHash[e];
            r &&
              r.forEach((e) => {
                try {
                  e(t.state);
                } catch {}
              });
          }
        }
        _onSubscriptionStateChange(e, t) {
          let r = this._subscriptionHashByClientSubscriptionId[e];
          if (null == r) return () => {};
          let i = (this._subscriptionStateChangeCallbacksByHash[r] ||=
            new Set());
          return (
            i.add(t),
            () => {
              i.delete(t),
                0 === i.size &&
                  delete this._subscriptionStateChangeCallbacksByHash[r];
            }
          );
        }
        async _updateSubscriptions() {
          if (0 === Object.keys(this._subscriptionsByHash).length) {
            this._rpcWebSocketConnected &&
              ((this._rpcWebSocketConnected = !1),
              (this._rpcWebSocketIdleTimeout = setTimeout(() => {
                this._rpcWebSocketIdleTimeout = null;
                try {
                  this._rpcWebSocket.close();
                } catch (e) {
                  e instanceof Error &&
                    console.log(
                      `Error when closing socket connection: ${e.message}`
                    );
                }
              }, 500)));
            return;
          }
          if (
            (null !== this._rpcWebSocketIdleTimeout &&
              (clearTimeout(this._rpcWebSocketIdleTimeout),
              (this._rpcWebSocketIdleTimeout = null),
              (this._rpcWebSocketConnected = !0)),
            !this._rpcWebSocketConnected)
          )
            return void this._rpcWebSocket.connect();
          let e = this._rpcWebSocketGeneration,
            t = () => e === this._rpcWebSocketGeneration;
          await Promise.all(
            Object.keys(this._subscriptionsByHash).map(async (e) => {
              let r = this._subscriptionsByHash[e];
              if (void 0 !== r)
                switch (r.state) {
                  case "pending":
                  case "unsubscribed":
                    if (0 === r.callbacks.size) {
                      delete this._subscriptionsByHash[e],
                        "unsubscribed" === r.state &&
                          delete this
                            ._subscriptionCallbacksByServerSubscriptionId[
                            r.serverSubscriptionId
                          ],
                        await this._updateSubscriptions();
                      return;
                    }
                    await (async () => {
                      let { args: i, method: s } = r;
                      try {
                        this._setSubscription(e, {
                          ...r,
                          state: "subscribing",
                        });
                        let t = await this._rpcWebSocket.call(s, i);
                        this._setSubscription(e, {
                          ...r,
                          serverSubscriptionId: t,
                          state: "subscribed",
                        }),
                          (this._subscriptionCallbacksByServerSubscriptionId[
                            t
                          ] = r.callbacks),
                          await this._updateSubscriptions();
                      } catch (n) {
                        if (
                          (n instanceof Error &&
                            console.error(
                              `${s} error for argument`,
                              i,
                              n.message
                            ),
                          !t())
                        )
                          return;
                        this._setSubscription(e, {
                          ...r,
                          state: "pending",
                        }),
                          await this._updateSubscriptions();
                      }
                    })();
                    break;
                  case "subscribed":
                    0 === r.callbacks.size &&
                      (await (async () => {
                        let { serverSubscriptionId: i, unsubscribeMethod: s } =
                          r;
                        if (this._subscriptionsAutoDisposedByRpc.has(i))
                          this._subscriptionsAutoDisposedByRpc.delete(i);
                        else {
                          this._setSubscription(e, {
                            ...r,
                            state: "unsubscribing",
                          }),
                            this._setSubscription(e, {
                              ...r,
                              state: "unsubscribing",
                            });
                          try {
                            await this._rpcWebSocket.call(s, [i]);
                          } catch (i) {
                            if (
                              (i instanceof Error &&
                                console.error(`${s} error:`, i.message),
                              !t())
                            )
                              return;
                            this._setSubscription(e, {
                              ...r,
                              state: "subscribed",
                            }),
                              await this._updateSubscriptions();
                            return;
                          }
                        }
                        this._setSubscription(e, {
                          ...r,
                          state: "unsubscribed",
                        }),
                          await this._updateSubscriptions();
                      })());
                }
            })
          );
        }
        _handleServerNotification(e, t) {
          let r = this._subscriptionCallbacksByServerSubscriptionId[e];
          void 0 !== r &&
            r.forEach((e) => {
              try {
                e(...t);
              } catch (e) {
                console.error(e);
              }
            });
        }
        _wsOnAccountNotification(e) {
          let { result: t, subscription: r } = (0, p.vt)(e, tT);
          this._handleServerNotification(r, [t.value, t.context]);
        }
        _makeSubscription(e, t) {
          let r = this._nextClientSubscriptionId++,
            i = eK([e.method, t]),
            s = this._subscriptionsByHash[i];
          return (
            void 0 === s
              ? (this._subscriptionsByHash[i] = {
                  ...e,
                  args: t,
                  callbacks: new Set([e.callback]),
                  state: "pending",
                })
              : s.callbacks.add(e.callback),
            (this._subscriptionHashByClientSubscriptionId[r] = i),
            (this._subscriptionDisposeFunctionsByClientSubscriptionId[r] =
              async () => {
                delete this._subscriptionDisposeFunctionsByClientSubscriptionId[
                  r
                ],
                  delete this._subscriptionHashByClientSubscriptionId[r];
                let t = this._subscriptionsByHash[i];
                J(
                  void 0 !== t,
                  `Could not find a \`Subscription\` when tearing down client subscription #${r}`
                ),
                  t.callbacks.delete(e.callback),
                  await this._updateSubscriptions();
              }),
            this._updateSubscriptions(),
            r
          );
        }
        onAccountChange(e, t, r) {
          let i = this._buildArgs(
            [e.toBase58()],
            r || this._commitment || "finalized",
            "base64"
          );
          return this._makeSubscription(
            {
              callback: t,
              method: "accountSubscribe",
              unsubscribeMethod: "accountUnsubscribe",
            },
            i
          );
        }
        async removeAccountChangeListener(e) {
          await this._unsubscribeClientSubscription(e, "account change");
        }
        _wsOnProgramAccountNotification(e) {
          let { result: t, subscription: r } = (0, p.vt)(e, tO);
          this._handleServerNotification(r, [
            {
              accountId: t.value.pubkey,
              accountInfo: t.value.account,
            },
            t.context,
          ]);
        }
        onProgramAccountChange(e, t, r, i) {
          let s = this._buildArgs(
            [e.toBase58()],
            r || this._commitment || "finalized",
            "base64",
            i
              ? {
                  filters: i,
                }
              : void 0
          );
          return this._makeSubscription(
            {
              callback: t,
              method: "programSubscribe",
              unsubscribeMethod: "programUnsubscribe",
            },
            s
          );
        }
        async removeProgramAccountChangeListener(e) {
          await this._unsubscribeClientSubscription(
            e,
            "program account change"
          );
        }
        onLogs(e, t, r) {
          let i = this._buildArgs(
            [
              "object" == typeof e
                ? {
                    mentions: [e.toString()],
                  }
                : e,
            ],
            r || this._commitment || "finalized"
          );
          return this._makeSubscription(
            {
              callback: t,
              method: "logsSubscribe",
              unsubscribeMethod: "logsUnsubscribe",
            },
            i
          );
        }
        async removeOnLogsListener(e) {
          await this._unsubscribeClientSubscription(e, "logs");
        }
        _wsOnLogsNotification(e) {
          let { result: t, subscription: r } = (0, p.vt)(e, rb);
          this._handleServerNotification(r, [t.value, t.context]);
        }
        _wsOnSlotNotification(e) {
          let { result: t, subscription: r } = (0, p.vt)(e, tR);
          this._handleServerNotification(r, [t]);
        }
        onSlotChange(e) {
          return this._makeSubscription(
            {
              callback: e,
              method: "slotSubscribe",
              unsubscribeMethod: "slotUnsubscribe",
            },
            []
          );
        }
        async removeSlotChangeListener(e) {
          await this._unsubscribeClientSubscription(e, "slot change");
        }
        _wsOnSlotUpdatesNotification(e) {
          let { result: t, subscription: r } = (0, p.vt)(e, tL);
          this._handleServerNotification(r, [t]);
        }
        onSlotUpdate(e) {
          return this._makeSubscription(
            {
              callback: e,
              method: "slotsUpdatesSubscribe",
              unsubscribeMethod: "slotsUpdatesUnsubscribe",
            },
            []
          );
        }
        async removeSlotUpdateListener(e) {
          await this._unsubscribeClientSubscription(e, "slot update");
        }
        async _unsubscribeClientSubscription(e, t) {
          let r = this._subscriptionDisposeFunctionsByClientSubscriptionId[e];
          r
            ? await r()
            : console.warn(
                `Ignored unsubscribe request because an active subscription with id \`${e}\` for '${t}' events could not be found.`
              );
        }
        _buildArgs(e, t, r, i) {
          let s = t || this._commitment;
          if (s || r || i) {
            let t = {};
            r && (t.encoding = r),
              s && (t.commitment = s),
              i && (t = Object.assign(t, i)),
              e.push(t);
          }
          return e;
        }
        _buildArgsAtLeastConfirmed(e, t, r, i) {
          let s = t || this._commitment;
          if (s && !["confirmed", "finalized"].includes(s))
            throw Error(
              "Using Connection with default commitment: `" +
                this._commitment +
                "`, but method requires at least `confirmed`"
            );
          return this._buildArgs(e, t, r, i);
        }
        _wsOnSignatureNotification(e) {
          let { result: t, subscription: r } = (0, p.vt)(e, tK);
          "receivedSignature" !== t.value &&
            this._subscriptionsAutoDisposedByRpc.add(r),
            this._handleServerNotification(
              r,
              "receivedSignature" === t.value
                ? [
                    {
                      type: "received",
                    },
                    t.context,
                  ]
                : [
                    {
                      type: "status",
                      result: t.value,
                    },
                    t.context,
                  ]
            );
        }
        onSignature(e, t, r) {
          let i = this._buildArgs([e], r || this._commitment || "finalized"),
            s = this._makeSubscription(
              {
                callback: (e, r) => {
                  if ("status" === e.type) {
                    t(e.result, r);
                    try {
                      this.removeSignatureListener(s);
                    } catch (e) {}
                  }
                },
                method: "signatureSubscribe",
                unsubscribeMethod: "signatureUnsubscribe",
              },
              i
            );
          return s;
        }
        onSignatureWithOptions(e, t, r) {
          let { commitment: i, ...s } = {
              ...r,
              commitment:
                (r && r.commitment) || this._commitment || "finalized",
            },
            n = this._buildArgs([e], i, void 0, s),
            o = this._makeSubscription(
              {
                callback: (e, r) => {
                  t(e, r);
                  try {
                    this.removeSignatureListener(o);
                  } catch (e) {}
                },
                method: "signatureSubscribe",
                unsubscribeMethod: "signatureUnsubscribe",
              },
              n
            );
          return o;
        }
        async removeSignatureListener(e) {
          await this._unsubscribeClientSubscription(e, "signature result");
        }
        _wsOnRootNotification(e) {
          let { result: t, subscription: r } = (0, p.vt)(e, tz);
          this._handleServerNotification(r, [t]);
        }
        onRootChange(e) {
          return this._makeSubscription(
            {
              callback: e,
              method: "rootSubscribe",
              unsubscribeMethod: "rootUnsubscribe",
            },
            []
          );
        }
        async removeRootChangeListener(e) {
          await this._unsubscribeClientSubscription(e, "root change");
        }
      }
      class rw {
        constructor(e) {
          (this._keypair = void 0), (this._keypair = e ?? I());
        }
        static generate() {
          return new rw(I());
        }
        static fromSecretKey(e, t) {
          if (64 !== e.byteLength) throw Error("bad secret key size");
          let r = e.slice(32, 64);
          if (!t || !t.skipValidation) {
            let t = _(e.slice(0, 32));
            for (let e = 0; e < 32; e++)
              if (r[e] !== t[e]) throw Error("provided secretKey is invalid");
          }
          return new rw({
            publicKey: r,
            secretKey: e,
          });
        }
        static fromSeed(e) {
          let t = _(e),
            r = new Uint8Array(64);
          return (
            r.set(e),
            r.set(t, 32),
            new rw({
              publicKey: t,
              secretKey: r,
            })
          );
        }
        get publicKey() {
          return new R(this._keypair.publicKey);
        }
        get secretKey() {
          return new Uint8Array(this._keypair.secretKey);
        }
      }
      let rS = Object.freeze({
        CreateLookupTable: {
          index: 0,
          layout: h.w3([
            h.DH("instruction"),
            eE("recentSlot"),
            h.u8("bumpSeed"),
          ]),
        },
        FreezeLookupTable: {
          index: 1,
          layout: h.w3([h.DH("instruction")]),
        },
        ExtendLookupTable: {
          index: 2,
          layout: h.w3([
            h.DH("instruction"),
            eE(),
            h.O6(U(), h.cY(h.DH(), -8), "addresses"),
          ]),
        },
        DeactivateLookupTable: {
          index: 3,
          layout: h.w3([h.DH("instruction")]),
        },
        CloseLookupTable: {
          index: 4,
          layout: h.w3([h.DH("instruction")]),
        },
      });
      class rI {
        constructor() {}
        static decodeInstructionType(e) {
          let t;
          this.checkProgramId(e.programId);
          let r = h.DH("instruction").decode(e.data);
          for (let [e, i] of Object.entries(rS))
            if (i.index == r) {
              t = e;
              break;
            }
          if (!t)
            throw Error(
              "Invalid Instruction. Should be a LookupTable Instruction"
            );
          return t;
        }
        static decodeCreateLookupTable(e) {
          this.checkProgramId(e.programId), this.checkKeysLength(e.keys, 4);
          let { recentSlot: t } = eS(rS.CreateLookupTable, e.data);
          return {
            authority: e.keys[1].pubkey,
            payer: e.keys[2].pubkey,
            recentSlot: Number(t),
          };
        }
        static decodeExtendLookupTable(e) {
          if ((this.checkProgramId(e.programId), e.keys.length < 2))
            throw Error(
              `invalid instruction; found ${e.keys.length} keys, expected at least 2`
            );
          let { addresses: t } = eS(rS.ExtendLookupTable, e.data);
          return {
            lookupTable: e.keys[0].pubkey,
            authority: e.keys[1].pubkey,
            payer: e.keys.length > 2 ? e.keys[2].pubkey : void 0,
            addresses: t.map((e) => new R(e)),
          };
        }
        static decodeCloseLookupTable(e) {
          return (
            this.checkProgramId(e.programId),
            this.checkKeysLength(e.keys, 3),
            {
              lookupTable: e.keys[0].pubkey,
              authority: e.keys[1].pubkey,
              recipient: e.keys[2].pubkey,
            }
          );
        }
        static decodeFreezeLookupTable(e) {
          return (
            this.checkProgramId(e.programId),
            this.checkKeysLength(e.keys, 2),
            {
              lookupTable: e.keys[0].pubkey,
              authority: e.keys[1].pubkey,
            }
          );
        }
        static decodeDeactivateLookupTable(e) {
          return (
            this.checkProgramId(e.programId),
            this.checkKeysLength(e.keys, 2),
            {
              lookupTable: e.keys[0].pubkey,
              authority: e.keys[1].pubkey,
            }
          );
        }
        static checkProgramId(e) {
          if (!e.equals(r_.programId))
            throw Error(
              "invalid instruction; programId is not AddressLookupTable Program"
            );
        }
        static checkKeysLength(e, t) {
          if (e.length < t)
            throw Error(
              `invalid instruction; found ${e.length} keys, expected at least ${t}`
            );
        }
      }
      class r_ {
        constructor() {}
        static createLookupTable(e) {
          let [t, r] = R.findProgramAddressSync(
              [e.authority.toBuffer(), (0, g.Bq)(BigInt(e.recentSlot), 8)],
              this.programId
            ),
            i = ew(rS.CreateLookupTable, {
              recentSlot: BigInt(e.recentSlot),
              bumpSeed: r,
            }),
            s = [
              {
                pubkey: t,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: e.authority,
                isSigner: !0,
                isWritable: !1,
              },
              {
                pubkey: e.payer,
                isSigner: !0,
                isWritable: !0,
              },
              {
                pubkey: eT.programId,
                isSigner: !1,
                isWritable: !1,
              },
            ];
          return [
            new en({
              programId: this.programId,
              keys: s,
              data: i,
            }),
            t,
          ];
        }
        static freezeLookupTable(e) {
          let t = ew(rS.FreezeLookupTable),
            r = [
              {
                pubkey: e.lookupTable,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: e.authority,
                isSigner: !0,
                isWritable: !1,
              },
            ];
          return new en({
            programId: this.programId,
            keys: r,
            data: t,
          });
        }
        static extendLookupTable(e) {
          let t = ew(rS.ExtendLookupTable, {
              addresses: e.addresses.map((e) => e.toBytes()),
            }),
            r = [
              {
                pubkey: e.lookupTable,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: e.authority,
                isSigner: !0,
                isWritable: !1,
              },
            ];
          return (
            e.payer &&
              r.push(
                {
                  pubkey: e.payer,
                  isSigner: !0,
                  isWritable: !0,
                },
                {
                  pubkey: eT.programId,
                  isSigner: !1,
                  isWritable: !1,
                }
              ),
            new en({
              programId: this.programId,
              keys: r,
              data: t,
            })
          );
        }
        static deactivateLookupTable(e) {
          let t = ew(rS.DeactivateLookupTable),
            r = [
              {
                pubkey: e.lookupTable,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: e.authority,
                isSigner: !0,
                isWritable: !1,
              },
            ];
          return new en({
            programId: this.programId,
            keys: r,
            data: t,
          });
        }
        static closeLookupTable(e) {
          let t = ew(rS.CloseLookupTable),
            r = [
              {
                pubkey: e.lookupTable,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: e.authority,
                isSigner: !0,
                isWritable: !1,
              },
              {
                pubkey: e.recipient,
                isSigner: !1,
                isWritable: !0,
              },
            ];
          return new en({
            programId: this.programId,
            keys: r,
            data: t,
          });
        }
      }
      r_.programId = new R("AddressLookupTab1e****************111111111");
      class rv {
        constructor() {}
        static decodeInstructionType(e) {
          let t;
          this.checkProgramId(e.programId);
          let r = h.u8("instruction").decode(e.data);
          for (let [e, i] of Object.entries(rA))
            if (i.index == r) {
              t = e;
              break;
            }
          if (!t)
            throw Error(
              "Instruction type incorrect; not a ComputeBudgetInstruction"
            );
          return t;
        }
        static decodeRequestUnits(e) {
          this.checkProgramId(e.programId);
          let { units: t, additionalFee: r } = eS(rA.RequestUnits, e.data);
          return {
            units: t,
            additionalFee: r,
          };
        }
        static decodeRequestHeapFrame(e) {
          this.checkProgramId(e.programId);
          let { bytes: t } = eS(rA.RequestHeapFrame, e.data);
          return {
            bytes: t,
          };
        }
        static decodeSetComputeUnitLimit(e) {
          this.checkProgramId(e.programId);
          let { units: t } = eS(rA.SetComputeUnitLimit, e.data);
          return {
            units: t,
          };
        }
        static decodeSetComputeUnitPrice(e) {
          this.checkProgramId(e.programId);
          let { microLamports: t } = eS(rA.SetComputeUnitPrice, e.data);
          return {
            microLamports: t,
          };
        }
        static checkProgramId(e) {
          if (!e.equals(rP.programId))
            throw Error(
              "invalid instruction; programId is not ComputeBudgetProgram"
            );
        }
      }
      let rA = Object.freeze({
        RequestUnits: {
          index: 0,
          layout: h.w3([
            h.u8("instruction"),
            h.DH("units"),
            h.DH("additionalFee"),
          ]),
        },
        RequestHeapFrame: {
          index: 1,
          layout: h.w3([h.u8("instruction"), h.DH("bytes")]),
        },
        SetComputeUnitLimit: {
          index: 2,
          layout: h.w3([h.u8("instruction"), h.DH("units")]),
        },
        SetComputeUnitPrice: {
          index: 3,
          layout: h.w3([h.u8("instruction"), eE("microLamports")]),
        },
      });
      class rP {
        constructor() {}
        static requestUnits(e) {
          let t = ew(rA.RequestUnits, e);
          return new en({
            keys: [],
            programId: this.programId,
            data: t,
          });
        }
        static requestHeapFrame(e) {
          let t = ew(rA.RequestHeapFrame, e);
          return new en({
            keys: [],
            programId: this.programId,
            data: t,
          });
        }
        static setComputeUnitLimit(e) {
          let t = ew(rA.SetComputeUnitLimit, e);
          return new en({
            keys: [],
            programId: this.programId,
            data: t,
          });
        }
        static setComputeUnitPrice(e) {
          let t = ew(rA.SetComputeUnitPrice, {
            microLamports: BigInt(e.microLamports),
          });
          return new en({
            keys: [],
            programId: this.programId,
            data: t,
          });
        }
      }
      rP.programId = new R("ComputeBudget****************11111111111111");
      let rE = h.w3([
        h.u8("numSignatures"),
        h.u8("padding"),
        h.NX("signatureOffset"),
        h.NX("signatureInstructionIndex"),
        h.NX("publicKeyOffset"),
        h.NX("publicKeyInstructionIndex"),
        h.NX("messageDataOffset"),
        h.NX("messageDataSize"),
        h.NX("messageInstructionIndex"),
      ]);
      class rB {
        constructor() {}
        static createInstructionWithPublicKey(e) {
          let {
            publicKey: t,
            message: r,
            signature: i,
            instructionIndex: n,
          } = e;
          J(
            32 === t.length,
            `Public Key must be 32 bytes but received ${t.length} bytes`
          ),
            J(
              64 === i.length,
              `Signature must be 64 bytes but received ${i.length} bytes`
            );
          let o = rE.span,
            a = o + t.length,
            c = a + i.length,
            u = s.Buffer.alloc(c + r.length),
            l = null == n ? 65535 : n;
          return (
            rE.encode(
              {
                numSignatures: 1,
                padding: 0,
                signatureOffset: a,
                signatureInstructionIndex: l,
                publicKeyOffset: o,
                publicKeyInstructionIndex: l,
                messageDataOffset: c,
                messageDataSize: r.length,
                messageInstructionIndex: l,
              },
              u
            ),
            u.fill(t, o),
            u.fill(i, a),
            u.fill(r, c),
            new en({
              keys: [],
              programId: rB.programId,
              data: u,
            })
          );
        }
        static createInstructionWithPrivateKey(e) {
          let { privateKey: t, message: r, instructionIndex: i } = e;
          J(
            64 === t.length,
            `Private key must be 64 bytes but received ${t.length} bytes`
          );
          try {
            let e = rw.fromSecretKey(t),
              s = e.publicKey.toBytes(),
              n = A(r, e.secretKey);
            return this.createInstructionWithPublicKey({
              publicKey: s,
              message: r,
              signature: n,
              instructionIndex: i,
            });
          } catch (e) {
            throw Error(`Error creating instruction; ${e}`);
          }
        }
      }
      rB.programId = new R("Ed25519SigVerify****************11111111111");
      let rW = (e, t) => {
        let r = w.bI.sign(e, t);
        return [r.toCompactRawBytes(), r.recovery];
      };
      w.bI.utils.isValidPrivateKey;
      let rT = w.bI.getPublicKey,
        rN = h.w3([
          h.u8("numSignatures"),
          h.NX("signatureOffset"),
          h.u8("signatureInstructionIndex"),
          h.NX("ethAddressOffset"),
          h.u8("ethAddressInstructionIndex"),
          h.NX("messageDataOffset"),
          h.NX("messageDataSize"),
          h.u8("messageInstructionIndex"),
          h.av(20, "ethAddress"),
          h.av(64, "signature"),
          h.u8("recoveryId"),
        ]);
      class rO {
        constructor() {}
        static publicKeyToEthAddress(e) {
          J(
            64 === e.length,
            `Public key must be 64 bytes but received ${e.length} bytes`
          );
          try {
            return s.Buffer.from((0, k.lY)(E(e))).slice(-20);
          } catch (e) {
            throw Error(`Error constructing Ethereum address: ${e}`);
          }
        }
        static createInstructionWithPublicKey(e) {
          let {
            publicKey: t,
            message: r,
            signature: i,
            recoveryId: s,
            instructionIndex: n,
          } = e;
          return rO.createInstructionWithEthAddress({
            ethAddress: rO.publicKeyToEthAddress(t),
            message: r,
            signature: i,
            recoveryId: s,
            instructionIndex: n,
          });
        }
        static createInstructionWithEthAddress(e) {
          let t,
            {
              ethAddress: r,
              message: i,
              signature: n,
              recoveryId: o,
              instructionIndex: a = 0,
            } = e;
          J(
            20 ===
              (t =
                "string" == typeof r
                  ? r.startsWith("0x")
                    ? s.Buffer.from(r.substr(2), "hex")
                    : s.Buffer.from(r, "hex")
                  : r).length,
            `Address must be 20 bytes but received ${t.length} bytes`
          );
          let c = 12 + t.length,
            u = c + n.length + 1,
            l = s.Buffer.alloc(rN.span + i.length);
          return (
            rN.encode(
              {
                numSignatures: 1,
                signatureOffset: c,
                signatureInstructionIndex: a,
                ethAddressOffset: 12,
                ethAddressInstructionIndex: a,
                messageDataOffset: u,
                messageDataSize: i.length,
                messageInstructionIndex: a,
                signature: E(n),
                ethAddress: E(t),
                recoveryId: o,
              },
              l
            ),
            l.fill(E(i), rN.span),
            new en({
              keys: [],
              programId: rO.programId,
              data: l,
            })
          );
        }
        static createInstructionWithPrivateKey(e) {
          let { privateKey: t, message: r, instructionIndex: i } = e;
          J(
            32 === t.length,
            `Private key must be 32 bytes but received ${t.length} bytes`
          );
          try {
            let e = E(t),
              n = rT(e, !1).slice(1),
              o = s.Buffer.from((0, k.lY)(E(r))),
              [a, c] = rW(o, e);
            return this.createInstructionWithPublicKey({
              publicKey: n,
              message: r,
              signature: a,
              recoveryId: c,
              instructionIndex: i,
            });
          } catch (e) {
            throw Error(`Error creating instruction; ${e}`);
          }
        }
      }
      rO.programId = new R("KeccakSecp256k****************1111111111111");
      let rC = new R("StakeConfig********************************");
      class rR {
        constructor(e, t) {
          (this.staker = void 0),
            (this.withdrawer = void 0),
            (this.staker = e),
            (this.withdrawer = t);
        }
      }
      class rx {
        constructor(e, t, r) {
          (this.unixTimestamp = void 0),
            (this.epoch = void 0),
            (this.custodian = void 0),
            (this.unixTimestamp = e),
            (this.epoch = t),
            (this.custodian = r);
        }
      }
      rx.default = new rx(0, 0, R.default);
      class rL {
        constructor() {}
        static decodeInstructionType(e) {
          let t;
          this.checkProgramId(e.programId);
          let r = h.DH("instruction").decode(e.data);
          for (let [e, i] of Object.entries(rK))
            if (i.index == r) {
              t = e;
              break;
            }
          if (!t)
            throw Error("Instruction type incorrect; not a StakeInstruction");
          return t;
        }
        static decodeInitialize(e) {
          this.checkProgramId(e.programId), this.checkKeyLength(e.keys, 2);
          let { authorized: t, lockup: r } = eS(rK.Initialize, e.data);
          return {
            stakePubkey: e.keys[0].pubkey,
            authorized: new rR(new R(t.staker), new R(t.withdrawer)),
            lockup: new rx(r.unixTimestamp, r.epoch, new R(r.custodian)),
          };
        }
        static decodeDelegate(e) {
          return (
            this.checkProgramId(e.programId),
            this.checkKeyLength(e.keys, 6),
            eS(rK.Delegate, e.data),
            {
              stakePubkey: e.keys[0].pubkey,
              votePubkey: e.keys[1].pubkey,
              authorizedPubkey: e.keys[5].pubkey,
            }
          );
        }
        static decodeAuthorize(e) {
          this.checkProgramId(e.programId), this.checkKeyLength(e.keys, 3);
          let { newAuthorized: t, stakeAuthorizationType: r } = eS(
              rK.Authorize,
              e.data
            ),
            i = {
              stakePubkey: e.keys[0].pubkey,
              authorizedPubkey: e.keys[2].pubkey,
              newAuthorizedPubkey: new R(t),
              stakeAuthorizationType: {
                index: r,
              },
            };
          return e.keys.length > 3 && (i.custodianPubkey = e.keys[3].pubkey), i;
        }
        static decodeAuthorizeWithSeed(e) {
          this.checkProgramId(e.programId), this.checkKeyLength(e.keys, 2);
          let {
              newAuthorized: t,
              stakeAuthorizationType: r,
              authoritySeed: i,
              authorityOwner: s,
            } = eS(rK.AuthorizeWithSeed, e.data),
            n = {
              stakePubkey: e.keys[0].pubkey,
              authorityBase: e.keys[1].pubkey,
              authoritySeed: i,
              authorityOwner: new R(s),
              newAuthorizedPubkey: new R(t),
              stakeAuthorizationType: {
                index: r,
              },
            };
          return e.keys.length > 3 && (n.custodianPubkey = e.keys[3].pubkey), n;
        }
        static decodeSplit(e) {
          this.checkProgramId(e.programId), this.checkKeyLength(e.keys, 3);
          let { lamports: t } = eS(rK.Split, e.data);
          return {
            stakePubkey: e.keys[0].pubkey,
            splitStakePubkey: e.keys[1].pubkey,
            authorizedPubkey: e.keys[2].pubkey,
            lamports: t,
          };
        }
        static decodeMerge(e) {
          return (
            this.checkProgramId(e.programId),
            this.checkKeyLength(e.keys, 3),
            eS(rK.Merge, e.data),
            {
              stakePubkey: e.keys[0].pubkey,
              sourceStakePubKey: e.keys[1].pubkey,
              authorizedPubkey: e.keys[4].pubkey,
            }
          );
        }
        static decodeWithdraw(e) {
          this.checkProgramId(e.programId), this.checkKeyLength(e.keys, 5);
          let { lamports: t } = eS(rK.Withdraw, e.data),
            r = {
              stakePubkey: e.keys[0].pubkey,
              toPubkey: e.keys[1].pubkey,
              authorizedPubkey: e.keys[4].pubkey,
              lamports: t,
            };
          return e.keys.length > 5 && (r.custodianPubkey = e.keys[5].pubkey), r;
        }
        static decodeDeactivate(e) {
          return (
            this.checkProgramId(e.programId),
            this.checkKeyLength(e.keys, 3),
            eS(rK.Deactivate, e.data),
            {
              stakePubkey: e.keys[0].pubkey,
              authorizedPubkey: e.keys[2].pubkey,
            }
          );
        }
        static checkProgramId(e) {
          if (!e.equals(rY.programId))
            throw Error("invalid instruction; programId is not StakeProgram");
        }
        static checkKeyLength(e, t) {
          if (e.length < t)
            throw Error(
              `invalid instruction; found ${e.length} keys, expected at least ${t}`
            );
        }
      }
      let rK = Object.freeze({
          Initialize: {
            index: 0,
            layout: h.w3([
              h.DH("instruction"),
              ((e = "authorized") => h.w3([U("staker"), U("withdrawer")], e))(),
              ((e = "lockup") =>
                h.w3(
                  [h.Wg("unixTimestamp"), h.Wg("epoch"), U("custodian")],
                  e
                ))(),
            ]),
          },
          Authorize: {
            index: 1,
            layout: h.w3([
              h.DH("instruction"),
              U("newAuthorized"),
              h.DH("stakeAuthorizationType"),
            ]),
          },
          Delegate: {
            index: 2,
            layout: h.w3([h.DH("instruction")]),
          },
          Split: {
            index: 3,
            layout: h.w3([h.DH("instruction"), h.Wg("lamports")]),
          },
          Withdraw: {
            index: 4,
            layout: h.w3([h.DH("instruction"), h.Wg("lamports")]),
          },
          Deactivate: {
            index: 5,
            layout: h.w3([h.DH("instruction")]),
          },
          Merge: {
            index: 7,
            layout: h.w3([h.DH("instruction")]),
          },
          AuthorizeWithSeed: {
            index: 8,
            layout: h.w3([
              h.DH("instruction"),
              U("newAuthorized"),
              h.DH("stakeAuthorizationType"),
              V("authoritySeed"),
              U("authorityOwner"),
            ]),
          },
        }),
        rz = Object.freeze({
          Staker: {
            index: 0,
          },
          Withdrawer: {
            index: 1,
          },
        });
      class rY {
        constructor() {}
        static initialize(e) {
          let { stakePubkey: t, authorized: r, lockup: i } = e,
            s = i || rx.default,
            n = ew(rK.Initialize, {
              authorized: {
                staker: E(r.staker.toBuffer()),
                withdrawer: E(r.withdrawer.toBuffer()),
              },
              lockup: {
                unixTimestamp: s.unixTimestamp,
                epoch: s.epoch,
                custodian: E(s.custodian.toBuffer()),
              },
            });
          return new en({
            keys: [
              {
                pubkey: t,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: eg,
                isSigner: !1,
                isWritable: !1,
              },
            ],
            programId: this.programId,
            data: n,
          });
        }
        static createAccountWithSeed(e) {
          let t = new eo();
          t.add(
            eT.createAccountWithSeed({
              fromPubkey: e.fromPubkey,
              newAccountPubkey: e.stakePubkey,
              basePubkey: e.basePubkey,
              seed: e.seed,
              lamports: e.lamports,
              space: this.space,
              programId: this.programId,
            })
          );
          let { stakePubkey: r, authorized: i, lockup: s } = e;
          return t.add(
            this.initialize({
              stakePubkey: r,
              authorized: i,
              lockup: s,
            })
          );
        }
        static createAccount(e) {
          let t = new eo();
          t.add(
            eT.createAccount({
              fromPubkey: e.fromPubkey,
              newAccountPubkey: e.stakePubkey,
              lamports: e.lamports,
              space: this.space,
              programId: this.programId,
            })
          );
          let { stakePubkey: r, authorized: i, lockup: s } = e;
          return t.add(
            this.initialize({
              stakePubkey: r,
              authorized: i,
              lockup: s,
            })
          );
        }
        static delegate(e) {
          let { stakePubkey: t, authorizedPubkey: r, votePubkey: i } = e,
            s = ew(rK.Delegate);
          return new eo().add({
            keys: [
              {
                pubkey: t,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: i,
                isSigner: !1,
                isWritable: !1,
              },
              {
                pubkey: eu,
                isSigner: !1,
                isWritable: !1,
              },
              {
                pubkey: eb,
                isSigner: !1,
                isWritable: !1,
              },
              {
                pubkey: rC,
                isSigner: !1,
                isWritable: !1,
              },
              {
                pubkey: r,
                isSigner: !0,
                isWritable: !1,
              },
            ],
            programId: this.programId,
            data: s,
          });
        }
        static authorize(e) {
          let {
              stakePubkey: t,
              authorizedPubkey: r,
              newAuthorizedPubkey: i,
              stakeAuthorizationType: s,
              custodianPubkey: n,
            } = e,
            o = ew(rK.Authorize, {
              newAuthorized: E(i.toBuffer()),
              stakeAuthorizationType: s.index,
            }),
            a = [
              {
                pubkey: t,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: eu,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: r,
                isSigner: !0,
                isWritable: !1,
              },
            ];
          return (
            n &&
              a.push({
                pubkey: n,
                isSigner: !0,
                isWritable: !1,
              }),
            new eo().add({
              keys: a,
              programId: this.programId,
              data: o,
            })
          );
        }
        static authorizeWithSeed(e) {
          let {
              stakePubkey: t,
              authorityBase: r,
              authoritySeed: i,
              authorityOwner: s,
              newAuthorizedPubkey: n,
              stakeAuthorizationType: o,
              custodianPubkey: a,
            } = e,
            c = ew(rK.AuthorizeWithSeed, {
              newAuthorized: E(n.toBuffer()),
              stakeAuthorizationType: o.index,
              authoritySeed: i,
              authorityOwner: E(s.toBuffer()),
            }),
            u = [
              {
                pubkey: t,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: r,
                isSigner: !0,
                isWritable: !1,
              },
              {
                pubkey: eu,
                isSigner: !1,
                isWritable: !1,
              },
            ];
          return (
            a &&
              u.push({
                pubkey: a,
                isSigner: !0,
                isWritable: !1,
              }),
            new eo().add({
              keys: u,
              programId: this.programId,
              data: c,
            })
          );
        }
        static splitInstruction(e) {
          let {
              stakePubkey: t,
              authorizedPubkey: r,
              splitStakePubkey: i,
              lamports: s,
            } = e,
            n = ew(rK.Split, {
              lamports: s,
            });
          return new en({
            keys: [
              {
                pubkey: t,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: i,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: r,
                isSigner: !0,
                isWritable: !1,
              },
            ],
            programId: this.programId,
            data: n,
          });
        }
        static split(e, t) {
          let r = new eo();
          return (
            r.add(
              eT.createAccount({
                fromPubkey: e.authorizedPubkey,
                newAccountPubkey: e.splitStakePubkey,
                lamports: t,
                space: this.space,
                programId: this.programId,
              })
            ),
            r.add(this.splitInstruction(e))
          );
        }
        static splitWithSeed(e, t) {
          let {
              stakePubkey: r,
              authorizedPubkey: i,
              splitStakePubkey: s,
              basePubkey: n,
              seed: o,
              lamports: a,
            } = e,
            c = new eo();
          return (
            c.add(
              eT.allocate({
                accountPubkey: s,
                basePubkey: n,
                seed: o,
                space: this.space,
                programId: this.programId,
              })
            ),
            t &&
              t > 0 &&
              c.add(
                eT.transfer({
                  fromPubkey: e.authorizedPubkey,
                  toPubkey: s,
                  lamports: t,
                })
              ),
            c.add(
              this.splitInstruction({
                stakePubkey: r,
                authorizedPubkey: i,
                splitStakePubkey: s,
                lamports: a,
              })
            )
          );
        }
        static merge(e) {
          let { stakePubkey: t, sourceStakePubKey: r, authorizedPubkey: i } = e,
            s = ew(rK.Merge);
          return new eo().add({
            keys: [
              {
                pubkey: t,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: r,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: eu,
                isSigner: !1,
                isWritable: !1,
              },
              {
                pubkey: eb,
                isSigner: !1,
                isWritable: !1,
              },
              {
                pubkey: i,
                isSigner: !0,
                isWritable: !1,
              },
            ],
            programId: this.programId,
            data: s,
          });
        }
        static withdraw(e) {
          let {
              stakePubkey: t,
              authorizedPubkey: r,
              toPubkey: i,
              lamports: s,
              custodianPubkey: n,
            } = e,
            o = ew(rK.Withdraw, {
              lamports: s,
            }),
            a = [
              {
                pubkey: t,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: i,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: eu,
                isSigner: !1,
                isWritable: !1,
              },
              {
                pubkey: eb,
                isSigner: !1,
                isWritable: !1,
              },
              {
                pubkey: r,
                isSigner: !0,
                isWritable: !1,
              },
            ];
          return (
            n &&
              a.push({
                pubkey: n,
                isSigner: !0,
                isWritable: !1,
              }),
            new eo().add({
              keys: a,
              programId: this.programId,
              data: o,
            })
          );
        }
        static deactivate(e) {
          let { stakePubkey: t, authorizedPubkey: r } = e,
            i = ew(rK.Deactivate);
          return new eo().add({
            keys: [
              {
                pubkey: t,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: eu,
                isSigner: !1,
                isWritable: !1,
              },
              {
                pubkey: r,
                isSigner: !0,
                isWritable: !1,
              },
            ],
            programId: this.programId,
            data: i,
          });
        }
      }
      (rY.programId = new R("Stake********************************111111")),
        (rY.space = 200);
      class rq {
        constructor(e, t, r, i) {
          (this.nodePubkey = void 0),
            (this.authorizedVoter = void 0),
            (this.authorizedWithdrawer = void 0),
            (this.commission = void 0),
            (this.nodePubkey = e),
            (this.authorizedVoter = t),
            (this.authorizedWithdrawer = r),
            (this.commission = i);
        }
      }
      class rD {
        constructor() {}
        static decodeInstructionType(e) {
          let t;
          this.checkProgramId(e.programId);
          let r = h.DH("instruction").decode(e.data);
          for (let [e, i] of Object.entries(rH))
            if (i.index == r) {
              t = e;
              break;
            }
          if (!t)
            throw Error("Instruction type incorrect; not a VoteInstruction");
          return t;
        }
        static decodeInitializeAccount(e) {
          this.checkProgramId(e.programId), this.checkKeyLength(e.keys, 4);
          let { voteInit: t } = eS(rH.InitializeAccount, e.data);
          return {
            votePubkey: e.keys[0].pubkey,
            nodePubkey: e.keys[3].pubkey,
            voteInit: new rq(
              new R(t.nodePubkey),
              new R(t.authorizedVoter),
              new R(t.authorizedWithdrawer),
              t.commission
            ),
          };
        }
        static decodeAuthorize(e) {
          this.checkProgramId(e.programId), this.checkKeyLength(e.keys, 3);
          let { newAuthorized: t, voteAuthorizationType: r } = eS(
            rH.Authorize,
            e.data
          );
          return {
            votePubkey: e.keys[0].pubkey,
            authorizedPubkey: e.keys[2].pubkey,
            newAuthorizedPubkey: new R(t),
            voteAuthorizationType: {
              index: r,
            },
          };
        }
        static decodeAuthorizeWithSeed(e) {
          this.checkProgramId(e.programId), this.checkKeyLength(e.keys, 3);
          let {
            voteAuthorizeWithSeedArgs: {
              currentAuthorityDerivedKeyOwnerPubkey: t,
              currentAuthorityDerivedKeySeed: r,
              newAuthorized: i,
              voteAuthorizationType: s,
            },
          } = eS(rH.AuthorizeWithSeed, e.data);
          return {
            currentAuthorityDerivedKeyBasePubkey: e.keys[2].pubkey,
            currentAuthorityDerivedKeyOwnerPubkey: new R(t),
            currentAuthorityDerivedKeySeed: r,
            newAuthorizedPubkey: new R(i),
            voteAuthorizationType: {
              index: s,
            },
            votePubkey: e.keys[0].pubkey,
          };
        }
        static decodeWithdraw(e) {
          this.checkProgramId(e.programId), this.checkKeyLength(e.keys, 3);
          let { lamports: t } = eS(rH.Withdraw, e.data);
          return {
            votePubkey: e.keys[0].pubkey,
            authorizedWithdrawerPubkey: e.keys[2].pubkey,
            lamports: t,
            toPubkey: e.keys[1].pubkey,
          };
        }
        static checkProgramId(e) {
          if (!e.equals(rU.programId))
            throw Error("invalid instruction; programId is not VoteProgram");
        }
        static checkKeyLength(e, t) {
          if (e.length < t)
            throw Error(
              `invalid instruction; found ${e.length} keys, expected at least ${t}`
            );
        }
      }
      let rH = Object.freeze({
          InitializeAccount: {
            index: 0,
            layout: h.w3([
              h.DH("instruction"),
              ((e = "voteInit") =>
                h.w3(
                  [
                    U("nodePubkey"),
                    U("authorizedVoter"),
                    U("authorizedWithdrawer"),
                    h.u8("commission"),
                  ],
                  e
                ))(),
            ]),
          },
          Authorize: {
            index: 1,
            layout: h.w3([
              h.DH("instruction"),
              U("newAuthorized"),
              h.DH("voteAuthorizationType"),
            ]),
          },
          Withdraw: {
            index: 3,
            layout: h.w3([h.DH("instruction"), h.Wg("lamports")]),
          },
          UpdateValidatorIdentity: {
            index: 4,
            layout: h.w3([h.DH("instruction")]),
          },
          AuthorizeWithSeed: {
            index: 10,
            layout: h.w3([
              h.DH("instruction"),
              ((e = "voteAuthorizeWithSeedArgs") =>
                h.w3(
                  [
                    h.DH("voteAuthorizationType"),
                    U("currentAuthorityDerivedKeyOwnerPubkey"),
                    V("currentAuthorityDerivedKeySeed"),
                    U("newAuthorized"),
                  ],
                  e
                ))(),
            ]),
          },
        }),
        rj = Object.freeze({
          Voter: {
            index: 0,
          },
          Withdrawer: {
            index: 1,
          },
        });
      class rU {
        constructor() {}
        static initializeAccount(e) {
          let { votePubkey: t, nodePubkey: r, voteInit: i } = e,
            s = ew(rH.InitializeAccount, {
              voteInit: {
                nodePubkey: E(i.nodePubkey.toBuffer()),
                authorizedVoter: E(i.authorizedVoter.toBuffer()),
                authorizedWithdrawer: E(i.authorizedWithdrawer.toBuffer()),
                commission: i.commission,
              },
            });
          return new en({
            keys: [
              {
                pubkey: t,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: eg,
                isSigner: !1,
                isWritable: !1,
              },
              {
                pubkey: eu,
                isSigner: !1,
                isWritable: !1,
              },
              {
                pubkey: r,
                isSigner: !0,
                isWritable: !1,
              },
            ],
            programId: this.programId,
            data: s,
          });
        }
        static createAccount(e) {
          let t = new eo();
          return (
            t.add(
              eT.createAccount({
                fromPubkey: e.fromPubkey,
                newAccountPubkey: e.votePubkey,
                lamports: e.lamports,
                space: this.space,
                programId: this.programId,
              })
            ),
            t.add(
              this.initializeAccount({
                votePubkey: e.votePubkey,
                nodePubkey: e.voteInit.nodePubkey,
                voteInit: e.voteInit,
              })
            )
          );
        }
        static authorize(e) {
          let {
              votePubkey: t,
              authorizedPubkey: r,
              newAuthorizedPubkey: i,
              voteAuthorizationType: s,
            } = e,
            n = ew(rH.Authorize, {
              newAuthorized: E(i.toBuffer()),
              voteAuthorizationType: s.index,
            });
          return new eo().add({
            keys: [
              {
                pubkey: t,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: eu,
                isSigner: !1,
                isWritable: !1,
              },
              {
                pubkey: r,
                isSigner: !0,
                isWritable: !1,
              },
            ],
            programId: this.programId,
            data: n,
          });
        }
        static authorizeWithSeed(e) {
          let {
              currentAuthorityDerivedKeyBasePubkey: t,
              currentAuthorityDerivedKeyOwnerPubkey: r,
              currentAuthorityDerivedKeySeed: i,
              newAuthorizedPubkey: s,
              voteAuthorizationType: n,
              votePubkey: o,
            } = e,
            a = ew(rH.AuthorizeWithSeed, {
              voteAuthorizeWithSeedArgs: {
                currentAuthorityDerivedKeyOwnerPubkey: E(r.toBuffer()),
                currentAuthorityDerivedKeySeed: i,
                newAuthorized: E(s.toBuffer()),
                voteAuthorizationType: n.index,
              },
            });
          return new eo().add({
            keys: [
              {
                pubkey: o,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: eu,
                isSigner: !1,
                isWritable: !1,
              },
              {
                pubkey: t,
                isSigner: !0,
                isWritable: !1,
              },
            ],
            programId: this.programId,
            data: a,
          });
        }
        static withdraw(e) {
          let {
              votePubkey: t,
              authorizedWithdrawerPubkey: r,
              lamports: i,
              toPubkey: s,
            } = e,
            n = ew(rH.Withdraw, {
              lamports: i,
            });
          return new eo().add({
            keys: [
              {
                pubkey: t,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: s,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: r,
                isSigner: !0,
                isWritable: !1,
              },
            ],
            programId: this.programId,
            data: n,
          });
        }
        static safeWithdraw(e, t, r) {
          if (e.lamports > t - r)
            throw Error(
              "Withdraw will leave vote account with insufficient funds."
            );
          return rU.withdraw(e);
        }
        static updateValidatorIdentity(e) {
          let {
              votePubkey: t,
              authorizedWithdrawerPubkey: r,
              nodePubkey: i,
            } = e,
            s = ew(rH.UpdateValidatorIdentity);
          return new eo().add({
            keys: [
              {
                pubkey: t,
                isSigner: !1,
                isWritable: !0,
              },
              {
                pubkey: i,
                isSigner: !0,
                isWritable: !1,
              },
              {
                pubkey: r,
                isSigner: !0,
                isWritable: !1,
              },
            ],
            programId: this.programId,
            data: s,
          });
        }
      }
      (rU.programId = new R("Vote********************************1111111")),
        (rU.space = 3762);
      let rM = new R("Va1idator1nfo****************11111111111111"),
        rV = (0, p.NW)({
          name: (0, p.Yj)(),
          website: (0, p.lq)((0, p.Yj)()),
          details: (0, p.lq)((0, p.Yj)()),
          keybaseUsername: (0, p.lq)((0, p.Yj)()),
        });
      class rF {
        constructor(e, t) {
          (this.key = void 0),
            (this.info = void 0),
            (this.key = e),
            (this.info = t);
        }
        static fromConfigData(e) {
          let t = [...e];
          if (2 !== F(t)) return null;
          let r = [];
          for (let e = 0; e < 2; e++) {
            let e = new R(Q(t, 0, O)),
              i = 1 === Z(t);
            r.push({
              publicKey: e,
              isSigner: i,
            });
          }
          if (r[0].publicKey.equals(rM) && r[1].isSigner) {
            let e = JSON.parse(V().decode(s.Buffer.from(t)));
            return (0, p.vA)(e, rV), new rF(r[1].publicKey, e);
          }
          return null;
        }
      }
      let r$ = new R("Vote********************************1111111"),
        rJ = h.w3([
          U("nodePubkey"),
          U("authorizedWithdrawer"),
          h.u8("commission"),
          h.I0(),
          h.O6(
            h.w3([h.I0("slot"), h.DH("confirmationCount")]),
            h.cY(h.DH(), -8),
            "votes"
          ),
          h.u8("rootSlotValid"),
          h.I0("rootSlot"),
          h.I0(),
          h.O6(
            h.w3([h.I0("epoch"), U("authorizedVoter")]),
            h.cY(h.DH(), -8),
            "authorizedVoters"
          ),
          h.w3(
            [
              h.O6(
                h.w3([
                  U("authorizedPubkey"),
                  h.I0("epochOfLastAuthorizedSwitch"),
                  h.I0("targetEpoch"),
                ]),
                32,
                "buf"
              ),
              h.I0("idx"),
              h.u8("isEmpty"),
            ],
            "priorVoters"
          ),
          h.I0(),
          h.O6(
            h.w3([h.I0("epoch"), h.I0("credits"), h.I0("prevCredits")]),
            h.cY(h.DH(), -8),
            "epochCredits"
          ),
          h.w3([h.I0("slot"), h.I0("timestamp")], "lastTimestamp"),
        ]);
      class rG {
        constructor(e) {
          (this.nodePubkey = void 0),
            (this.authorizedWithdrawer = void 0),
            (this.commission = void 0),
            (this.rootSlot = void 0),
            (this.votes = void 0),
            (this.authorizedVoters = void 0),
            (this.priorVoters = void 0),
            (this.epochCredits = void 0),
            (this.lastTimestamp = void 0),
            (this.nodePubkey = e.nodePubkey),
            (this.authorizedWithdrawer = e.authorizedWithdrawer),
            (this.commission = e.commission),
            (this.rootSlot = e.rootSlot),
            (this.votes = e.votes),
            (this.authorizedVoters = e.authorizedVoters),
            (this.priorVoters = e.priorVoters),
            (this.epochCredits = e.epochCredits),
            (this.lastTimestamp = e.lastTimestamp);
        }
        static fromAccountData(e) {
          let t = rJ.decode(E(e), 4),
            r = t.rootSlot;
          return (
            t.rootSlotValid || (r = null),
            new rG({
              nodePubkey: new R(t.nodePubkey),
              authorizedWithdrawer: new R(t.authorizedWithdrawer),
              commission: t.commission,
              votes: t.votes,
              rootSlot: r,
              authorizedVoters: t.authorizedVoters.map(rX),
              priorVoters: (function ({ buf: e, idx: t, isEmpty: r }) {
                return r
                  ? []
                  : [...e.slice(t + 1).map(rZ), ...e.slice(0, t).map(rZ)];
              })(t.priorVoters),
              epochCredits: t.epochCredits,
              lastTimestamp: t.lastTimestamp,
            })
          );
        }
      }
      function rX({ authorizedVoter: e, epoch: t }) {
        return {
          epoch: t,
          authorizedVoter: new R(e),
        };
      }
      function rZ({
        authorizedPubkey: e,
        epochOfLastAuthorizedSwitch: t,
        targetEpoch: r,
      }) {
        return {
          authorizedPubkey: new R(e),
          epochOfLastAuthorizedSwitch: t,
          targetEpoch: r,
        };
      }
      let rQ = {
        http: {
          devnet: "http://api.devnet.solana.com",
          testnet: "http://api.testnet.solana.com",
          "mainnet-beta": "http://api.mainnet-beta.solana.com/",
        },
        https: {
          devnet: "https://api.devnet.solana.com",
          testnet: "https://api.testnet.solana.com",
          "mainnet-beta": "https://api.mainnet-beta.solana.com/",
        },
      };
      function r0(e, t) {
        let r = !1 === t ? "http" : "https";
        if (!e) return rQ[r].devnet;
        let i = rQ[r][e];
        if (!i) throw Error(`Unknown ${r} cluster: ${e}`);
        return i;
      }
      async function r1(e, t, r, i) {
        let s, n;
        (r &&
          Object.prototype.hasOwnProperty.call(r, "lastValidBlockHeight")) ||
        (r && Object.prototype.hasOwnProperty.call(r, "nonceValue"))
          ? ((s = r), (n = i))
          : (n = r);
        let o = n && {
            skipPreflight: n.skipPreflight,
            preflightCommitment: n.preflightCommitment || n.commitment,
            minContextSlot: n.minContextSlot,
          },
          a = await e.sendRawTransaction(t, o),
          c = n && n.commitment,
          u = s ? e.confirmTransaction(s, c) : e.confirmTransaction(a, c),
          l = (await u).value;
        if (l.err)
          throw Error(`Raw transaction ${a} failed (${JSON.stringify(l)})`);
        return a;
      }
      let r3 = 1e9;
    },
  },
]);
(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
  [8615],
  {
    8379: () => {},
    47380: (e, a, t) => {
      "use strict";
      t.d(a, {
        $5: () => i,
        $W: () => d,
        FM: () => n,
        SR: () => c,
        VE: () => s,
        eT: () => m,
        gV: () => l,
        g_: () => r,
        uu: () => o,
      });
      var n = {
          address: "H5RnrnQFVYiGCsGomawwyZ1gJgmMsSXDYbpidZredcGZ",
          metadata: {
            name: "the_backwoods",
            version: "0.1.0",
            spec: "0.1.0",
            description: "Created with Anchor",
          },
          instructions: [
            {
              name: "add_funds",
              discriminator: [132, 237, 76, 57, 80, 10, 179, 138],
              accounts: [
                {
                  name: "staking_pool",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [115, 116, 97, 107, 101],
                      },
                      {
                        kind: "account",
                        path: "staking_pool.collection",
                        account: "StakingPool",
                      },
                      {
                        kind: "account",
                        path: "staking_pool.owner",
                        account: "StakingPool",
                      },
                    ],
                  },
                },
                {
                  name: "reward_mint",
                  relations: ["staking_pool"],
                },
                {
                  name: "token_account",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "owner",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "reward_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "stake_token_vault",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "token_authority",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "reward_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "token_authority",
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [
                          116, 111, 107, 101, 110, 45, 97, 117, 116, 104, 111,
                          114, 105, 116, 121,
                        ],
                      },
                      {
                        kind: "account",
                        path: "staking_pool",
                      },
                    ],
                  },
                },
                {
                  name: "owner",
                  signer: !0,
                  relations: ["staking_pool"],
                },
                {
                  name: "token_program",
                  address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                },
              ],
              args: [
                {
                  name: "amount",
                  type: "u64",
                },
              ],
            },
            {
              name: "init_staking",
              discriminator: [42, 18, 242, 224, 66, 32, 122, 8],
              accounts: [
                {
                  name: "staking_pool",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [115, 116, 97, 107, 101],
                      },
                      {
                        kind: "account",
                        path: "collection_address",
                      },
                      {
                        kind: "account",
                        path: "owner",
                      },
                    ],
                  },
                },
                {
                  name: "token_mint",
                },
                {
                  name: "token_account",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "owner",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "token_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "stake_token_vault",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "token_authority",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "token_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "collection_address",
                },
                {
                  name: "owner",
                  writable: !0,
                  signer: !0,
                },
                {
                  name: "token_authority",
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [
                          116, 111, 107, 101, 110, 95, 97, 117, 116, 104, 111,
                          114, 105, 116, 121,
                        ],
                      },
                      {
                        kind: "account",
                        path: "staking_pool",
                      },
                    ],
                  },
                },
                {
                  name: "token_program",
                  address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                },
                {
                  name: "associated_token_program",
                  address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
                },
                {
                  name: "system_program",
                  address: "********************************",
                },
              ],
              args: [
                {
                  name: "reward_rate",
                  type: "u64",
                },
                {
                  name: "minimum_period",
                  type: "i64",
                },
                {
                  name: "staking_starts_at",
                  type: "i64",
                },
                {
                  name: "staking_ends_at",
                  type: "i64",
                },
                {
                  name: "max_stakers_count",
                  type: "u64",
                },
                {
                  name: "require_owner_signature",
                  type: "bool",
                },
              ],
            },
            {
              name: "stake",
              discriminator: [206, 176, 202, 18, 200, 209, 179, 108],
              accounts: [
                {
                  name: "staking_pool",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [115, 116, 97, 107, 101],
                      },
                      {
                        kind: "account",
                        path: "staking_pool.collection",
                        account: "StakingPool",
                      },
                      {
                        kind: "account",
                        path: "staking_pool.owner",
                        account: "StakingPool",
                      },
                    ],
                  },
                },
                {
                  name: "staker_account",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [
                          115, 116, 97, 107, 101, 114, 95, 97, 99, 99, 111, 117,
                          110, 116,
                        ],
                      },
                      {
                        kind: "account",
                        path: "staking_pool",
                      },
                      {
                        kind: "account",
                        path: "nft_mint",
                      },
                    ],
                  },
                },
                {
                  name: "nft_mint",
                },
                {
                  name: "nft_token",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "signer",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "nft_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "nft_metadata",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [109, 101, 116, 97, 100, 97, 116, 97],
                      },
                      {
                        kind: "const",
                        value: [
                          11, 112, 101, 177, 227, 209, 124, 69, 56, 157, 82,
                          127, 107, 4, 195, 205, 88, 184, 108, 115, 26, 160,
                          253, 181, 73, 182, 209, 188, 3, 248, 41, 70,
                        ],
                      },
                      {
                        kind: "account",
                        path: "nft_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        11, 112, 101, 177, 227, 209, 124, 69, 56, 157, 82, 127,
                        107, 4, 195, 205, 88, 184, 108, 115, 26, 160, 253, 181,
                        73, 182, 209, 188, 3, 248, 41, 70,
                      ],
                    },
                  },
                },
                {
                  name: "nft_token_record",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [109, 101, 116, 97, 100, 97, 116, 97],
                      },
                      {
                        kind: "const",
                        value: [
                          11, 112, 101, 177, 227, 209, 124, 69, 56, 157, 82,
                          127, 107, 4, 195, 205, 88, 184, 108, 115, 26, 160,
                          253, 181, 73, 182, 209, 188, 3, 248, 41, 70,
                        ],
                      },
                      {
                        kind: "account",
                        path: "nft_mint",
                      },
                      {
                        kind: "const",
                        value: [
                          116, 111, 107, 101, 110, 95, 114, 101, 99, 111, 114,
                          100,
                        ],
                      },
                      {
                        kind: "account",
                        path: "nft_token",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        11, 112, 101, 177, 227, 209, 124, 69, 56, 157, 82, 127,
                        107, 4, 195, 205, 88, 184, 108, 115, 26, 160, 253, 181,
                        73, 182, 209, 188, 3, 248, 41, 70,
                      ],
                    },
                  },
                },
                {
                  name: "nft_edition",
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [109, 101, 116, 97, 100, 97, 116, 97],
                      },
                      {
                        kind: "const",
                        value: [
                          11, 112, 101, 177, 227, 209, 124, 69, 56, 157, 82,
                          127, 107, 4, 195, 205, 88, 184, 108, 115, 26, 160,
                          253, 181, 73, 182, 209, 188, 3, 248, 41, 70,
                        ],
                      },
                      {
                        kind: "account",
                        path: "nft_mint",
                      },
                      {
                        kind: "const",
                        value: [101, 100, 105, 116, 105, 111, 110],
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        11, 112, 101, 177, 227, 209, 124, 69, 56, 157, 82, 127,
                        107, 4, 195, 205, 88, 184, 108, 115, 26, 160, 253, 181,
                        73, 182, 209, 188, 3, 248, 41, 70,
                      ],
                    },
                  },
                },
                {
                  name: "nft_ruleset",
                },
                {
                  name: "signer",
                  writable: !0,
                  signer: !0,
                },
                {
                  name: "pool_owner",
                  writable: !0,
                  signer: !0,
                  optional: !0,
                },
                {
                  name: "token_program",
                  address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                },
                {
                  name: "associated_token_program",
                  address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
                },
                {
                  name: "token_metadata_program",
                  address: "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s",
                },
                {
                  name: "system_program",
                  address: "********************************",
                },
                {
                  name: "authorization_rules_program",
                },
                {
                  name: "sysvar_instructions",
                },
              ],
              args: [],
            },
            {
              name: "unstake",
              discriminator: [90, 95, 107, 42, 205, 124, 50, 225],
              accounts: [
                {
                  name: "staking_pool",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [115, 116, 97, 107, 101],
                      },
                      {
                        kind: "account",
                        path: "staking_pool.collection",
                        account: "StakingPool",
                      },
                      {
                        kind: "account",
                        path: "staking_pool.owner",
                        account: "StakingPool",
                      },
                    ],
                  },
                },
                {
                  name: "staker_account",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [
                          115, 116, 97, 107, 101, 114, 95, 97, 99, 99, 111, 117,
                          110, 116,
                        ],
                      },
                      {
                        kind: "account",
                        path: "staking_pool",
                      },
                      {
                        kind: "account",
                        path: "staker_account.nft_mint",
                        account: "StakerAccount",
                      },
                    ],
                  },
                },
                {
                  name: "reward_mint",
                  relations: ["staking_pool"],
                },
                {
                  name: "stake_token_vault",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "token_authority",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "reward_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "reward_receive_account",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "staker",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "reward_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "nft_mint",
                  relations: ["staker_account"],
                },
                {
                  name: "nft_token",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "staker",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "nft_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "nft_metadata",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [109, 101, 116, 97, 100, 97, 116, 97],
                      },
                      {
                        kind: "const",
                        value: [
                          11, 112, 101, 177, 227, 209, 124, 69, 56, 157, 82,
                          127, 107, 4, 195, 205, 88, 184, 108, 115, 26, 160,
                          253, 181, 73, 182, 209, 188, 3, 248, 41, 70,
                        ],
                      },
                      {
                        kind: "account",
                        path: "nft_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        11, 112, 101, 177, 227, 209, 124, 69, 56, 157, 82, 127,
                        107, 4, 195, 205, 88, 184, 108, 115, 26, 160, 253, 181,
                        73, 182, 209, 188, 3, 248, 41, 70,
                      ],
                    },
                  },
                },
                {
                  name: "nft_token_record",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [109, 101, 116, 97, 100, 97, 116, 97],
                      },
                      {
                        kind: "const",
                        value: [
                          11, 112, 101, 177, 227, 209, 124, 69, 56, 157, 82,
                          127, 107, 4, 195, 205, 88, 184, 108, 115, 26, 160,
                          253, 181, 73, 182, 209, 188, 3, 248, 41, 70,
                        ],
                      },
                      {
                        kind: "account",
                        path: "nft_mint",
                      },
                      {
                        kind: "const",
                        value: [
                          116, 111, 107, 101, 110, 95, 114, 101, 99, 111, 114,
                          100,
                        ],
                      },
                      {
                        kind: "account",
                        path: "nft_token",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        11, 112, 101, 177, 227, 209, 124, 69, 56, 157, 82, 127,
                        107, 4, 195, 205, 88, 184, 108, 115, 26, 160, 253, 181,
                        73, 182, 209, 188, 3, 248, 41, 70,
                      ],
                    },
                  },
                },
                {
                  name: "nft_edition",
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [109, 101, 116, 97, 100, 97, 116, 97],
                      },
                      {
                        kind: "const",
                        value: [
                          11, 112, 101, 177, 227, 209, 124, 69, 56, 157, 82,
                          127, 107, 4, 195, 205, 88, 184, 108, 115, 26, 160,
                          253, 181, 73, 182, 209, 188, 3, 248, 41, 70,
                        ],
                      },
                      {
                        kind: "account",
                        path: "nft_mint",
                      },
                      {
                        kind: "const",
                        value: [101, 100, 105, 116, 105, 111, 110],
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        11, 112, 101, 177, 227, 209, 124, 69, 56, 157, 82, 127,
                        107, 4, 195, 205, 88, 184, 108, 115, 26, 160, 253, 181,
                        73, 182, 209, 188, 3, 248, 41, 70,
                      ],
                    },
                  },
                },
                {
                  name: "nft_receive_account",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "staker",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "nft_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "token_authority",
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [
                          116, 111, 107, 101, 110, 95, 97, 117, 116, 104, 111,
                          114, 105, 116, 121,
                        ],
                      },
                      {
                        kind: "account",
                        path: "staking_pool",
                      },
                    ],
                  },
                },
                {
                  name: "staker",
                  writable: !0,
                  signer: !0,
                  relations: ["staker_account"],
                },
                {
                  name: "pool_owner",
                  writable: !0,
                  signer: !0,
                  optional: !0,
                },
                {
                  name: "token_program",
                  address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                },
                {
                  name: "associated_token_program",
                  address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
                },
                {
                  name: "token_metadata_program",
                  address: "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s",
                },
                {
                  name: "system_program",
                  address: "********************************",
                },
                {
                  name: "sysvar_instructions",
                },
              ],
              args: [],
            },
            {
              name: "unstake_owner",
              discriminator: [124, 163, 63, 202, 123, 128, 9, 80],
              accounts: [
                {
                  name: "staking_pool",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [115, 116, 97, 107, 101],
                      },
                      {
                        kind: "account",
                        path: "staking_pool.collection",
                        account: "StakingPool",
                      },
                      {
                        kind: "account",
                        path: "staking_pool.owner",
                        account: "StakingPool",
                      },
                    ],
                  },
                },
                {
                  name: "staker_account",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [
                          115, 116, 97, 107, 101, 114, 95, 97, 99, 99, 111, 117,
                          110, 116,
                        ],
                      },
                      {
                        kind: "account",
                        path: "staking_pool",
                      },
                      {
                        kind: "account",
                        path: "staker_account.nft_mint",
                        account: "StakerAccount",
                      },
                    ],
                  },
                },
                {
                  name: "reward_mint",
                  relations: ["staking_pool"],
                },
                {
                  name: "stake_token_vault",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "token_authority",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "reward_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "reward_receive_account",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "staker",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "reward_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "nft_mint",
                  relations: ["staker_account"],
                },
                {
                  name: "nft_token",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "staker",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "nft_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "nft_metadata",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [109, 101, 116, 97, 100, 97, 116, 97],
                      },
                      {
                        kind: "const",
                        value: [
                          11, 112, 101, 177, 227, 209, 124, 69, 56, 157, 82,
                          127, 107, 4, 195, 205, 88, 184, 108, 115, 26, 160,
                          253, 181, 73, 182, 209, 188, 3, 248, 41, 70,
                        ],
                      },
                      {
                        kind: "account",
                        path: "nft_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        11, 112, 101, 177, 227, 209, 124, 69, 56, 157, 82, 127,
                        107, 4, 195, 205, 88, 184, 108, 115, 26, 160, 253, 181,
                        73, 182, 209, 188, 3, 248, 41, 70,
                      ],
                    },
                  },
                },
                {
                  name: "nft_token_record",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [109, 101, 116, 97, 100, 97, 116, 97],
                      },
                      {
                        kind: "const",
                        value: [
                          11, 112, 101, 177, 227, 209, 124, 69, 56, 157, 82,
                          127, 107, 4, 195, 205, 88, 184, 108, 115, 26, 160,
                          253, 181, 73, 182, 209, 188, 3, 248, 41, 70,
                        ],
                      },
                      {
                        kind: "account",
                        path: "nft_mint",
                      },
                      {
                        kind: "const",
                        value: [
                          116, 111, 107, 101, 110, 95, 114, 101, 99, 111, 114,
                          100,
                        ],
                      },
                      {
                        kind: "account",
                        path: "nft_token",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        11, 112, 101, 177, 227, 209, 124, 69, 56, 157, 82, 127,
                        107, 4, 195, 205, 88, 184, 108, 115, 26, 160, 253, 181,
                        73, 182, 209, 188, 3, 248, 41, 70,
                      ],
                    },
                  },
                },
                {
                  name: "nft_edition",
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [109, 101, 116, 97, 100, 97, 116, 97],
                      },
                      {
                        kind: "const",
                        value: [
                          11, 112, 101, 177, 227, 209, 124, 69, 56, 157, 82,
                          127, 107, 4, 195, 205, 88, 184, 108, 115, 26, 160,
                          253, 181, 73, 182, 209, 188, 3, 248, 41, 70,
                        ],
                      },
                      {
                        kind: "account",
                        path: "nft_mint",
                      },
                      {
                        kind: "const",
                        value: [101, 100, 105, 116, 105, 111, 110],
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        11, 112, 101, 177, 227, 209, 124, 69, 56, 157, 82, 127,
                        107, 4, 195, 205, 88, 184, 108, 115, 26, 160, 253, 181,
                        73, 182, 209, 188, 3, 248, 41, 70,
                      ],
                    },
                  },
                },
                {
                  name: "nft_receive_account",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "staker",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "nft_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "token_authority",
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [
                          116, 111, 107, 101, 110, 95, 97, 117, 116, 104, 111,
                          114, 105, 116, 121,
                        ],
                      },
                      {
                        kind: "account",
                        path: "staking_pool",
                      },
                    ],
                  },
                },
                {
                  name: "staker",
                  writable: !0,
                  relations: ["staker_account"],
                },
                {
                  name: "pool_owner",
                  writable: !0,
                  signer: !0,
                },
                {
                  name: "token_program",
                  address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                },
                {
                  name: "associated_token_program",
                  address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
                },
                {
                  name: "token_metadata_program",
                  address: "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s",
                },
                {
                  name: "system_program",
                  address: "********************************",
                },
                {
                  name: "sysvar_instructions",
                },
              ],
              args: [],
            },
            {
              name: "withdraw_rewards",
              discriminator: [10, 214, 219, 139, 205, 22, 251, 21],
              accounts: [
                {
                  name: "staking_pool",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [115, 116, 97, 107, 101],
                      },
                      {
                        kind: "account",
                        path: "staking_pool.collection",
                        account: "StakingPool",
                      },
                      {
                        kind: "account",
                        path: "staking_pool.owner",
                        account: "StakingPool",
                      },
                    ],
                  },
                },
                {
                  name: "staker_account",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [
                          115, 116, 97, 107, 101, 114, 95, 97, 99, 99, 111, 117,
                          110, 116,
                        ],
                      },
                      {
                        kind: "account",
                        path: "staking_pool",
                      },
                      {
                        kind: "account",
                        path: "staker_account.nft_mint",
                        account: "StakerAccount",
                      },
                    ],
                  },
                },
                {
                  name: "reward_mint",
                  relations: ["staking_pool"],
                },
                {
                  name: "stake_token_vault",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "token_authority",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "reward_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "reward_receive_account",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "staker",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "reward_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "token_authority",
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [
                          116, 111, 107, 101, 110, 95, 97, 117, 116, 104, 111,
                          114, 105, 116, 121,
                        ],
                      },
                      {
                        kind: "account",
                        path: "staking_pool",
                      },
                    ],
                  },
                },
                {
                  name: "staker",
                  writable: !0,
                  signer: !0,
                  relations: ["staker_account"],
                },
                {
                  name: "token_program",
                  address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                },
                {
                  name: "associated_token_program",
                  address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
                },
                {
                  name: "system_program",
                  address: "********************************",
                },
              ],
              args: [],
            },
          ],
          accounts: [
            {
              name: "StakerAccount",
              discriminator: [12, 152, 43, 218, 164, 11, 150, 174],
            },
            {
              name: "StakingPool",
              discriminator: [203, 19, 214, 220, 220, 154, 24, 102],
            },
          ],
          errors: [
            {
              code: 6e3,
              name: "StakeBumpError",
              msg: "unable to get staking pool bump",
            },
            {
              code: 6001,
              name: "NftBumpError",
              msg: "unable to get nft record bump",
            },
            {
              code: 6002,
              name: "NegativePeriodValue",
              msg: "the minimum staking period in secs can't be negative",
            },
            {
              code: 6003,
              name: "InvalidStakeEndTime",
              msg: "stake ends time must be greater than the current time & start time",
            },
            {
              code: 6004,
              name: "TokenNotNFT",
              msg: "the given mint account doesn't belong to NFT",
            },
            {
              code: 6005,
              name: "TokenAccountEmpty",
              msg: "the given token account has no token",
            },
            {
              code: 6006,
              name: "CollectionNotVerified",
              msg: "the collection field in the metadata is not verified",
            },
            {
              code: 6007,
              name: "InvalidCollection",
              msg: "the collection doesn't match the staking details",
            },
            {
              code: 6008,
              name: "MaxStakersReached",
              msg: "max staker count reached",
            },
            {
              code: 6009,
              name: "IneligibleForReward",
              msg: "the minimum stake period for the rewards not completed yet",
            },
            {
              code: 6010,
              name: "StakingIsOver",
              msg: "the nft stake time is greator than the staking period",
            },
            {
              code: 6011,
              name: "StakingNotLive",
              msg: "the staking is not yet started",
            },
            {
              code: 6012,
              name: "StakingInactive",
              msg: "the staking is not currently active",
            },
            {
              code: 6013,
              name: "InsufficientBalInVault",
              msg: "Insufficient tokens in Vault to extend the period or reward",
            },
            {
              code: 6014,
              name: "FailedTimeConversion",
              msg: "failed to convert the time to u64",
            },
            {
              code: 6015,
              name: "FailedWeightConversion",
              msg: "failed to convert the weight to u64",
            },
            {
              code: 6016,
              name: "ProgramAddError",
              msg: "unable to add the given values",
            },
            {
              code: 6017,
              name: "ProgramSubError",
              msg: "unable to subtract the given values",
            },
            {
              code: 6018,
              name: "ProgramMulError",
              msg: "unable to multiply the given values",
            },
            {
              code: 6019,
              name: "ProgramDivError",
              msg: "unable to divide the given values",
            },
            {
              code: 6020,
              name: "NoPoolOwnerSignatureError",
              msg: "valid pool owner signature missing",
            },
          ],
          types: [
            {
              name: "StakerAccount",
              type: {
                kind: "struct",
                fields: [
                  {
                    name: "staker",
                    docs: ["The owner/staker (32)"],
                    type: "pubkey",
                  },
                  {
                    name: "nft_mint",
                    docs: ["The mint of the staked NFT (32)"],
                    type: "pubkey",
                  },
                  {
                    name: "staked_at",
                    docs: ["The staking timestamp (8)"],
                    type: "i64",
                  },
                  {
                    name: "bump",
                    docs: ["The bump of Staker Account PDA (1)"],
                    type: "u8",
                  },
                ],
              },
            },
            {
              name: "StakingPool",
              type: {
                kind: "struct",
                fields: [
                  {
                    name: "is_active",
                    type: "bool",
                  },
                  {
                    name: "owner",
                    type: "pubkey",
                  },
                  {
                    name: "reward_mint",
                    type: "pubkey",
                  },
                  {
                    name: "reward_rate",
                    type: {
                      vec: "u64",
                    },
                  },
                  {
                    name: "reward_change_time",
                    type: {
                      vec: "i64",
                    },
                  },
                  {
                    name: "collection",
                    type: "pubkey",
                  },
                  {
                    name: "max_staked_count",
                    type: "u64",
                  },
                  {
                    name: "current_staked_count",
                    type: "u64",
                  },
                  {
                    name: "staked_weight",
                    type: "u128",
                  },
                  {
                    name: "staking_starts_at",
                    type: "i64",
                  },
                  {
                    name: "staking_ends_at",
                    type: "i64",
                  },
                  {
                    name: "minimum_period",
                    type: "i64",
                  },
                  {
                    name: "stake_bump",
                    type: "u8",
                  },
                  {
                    name: "token_auth_bump",
                    type: "u8",
                  },
                  {
                    name: "current_balance",
                    type: "u64",
                  },
                  {
                    name: "require_owner_signature",
                    type: "bool",
                  },
                ],
              },
            },
          ],
          constants: [
            {
              name: "WEIGHT",
              type: "u128",
              value: "**********",
            },
          ],
        },
        i = {
          address: "3hWzGFfDUUAMgdk31XVddzRU2QGLRxV7LhykdSLWtQzm",
          metadata: {
            name: "the_backwoods_tokens",
            version: "0.1.0",
            spec: "0.1.0",
            description: "Created with Anchor",
          },
          instructions: [
            {
              name: "add_funds",
              discriminator: [132, 237, 76, 57, 80, 10, 179, 138],
              accounts: [
                {
                  name: "token_vault",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [
                          116, 111, 107, 101, 110, 95, 118, 97, 117, 108, 116,
                        ],
                      },
                      {
                        kind: "account",
                        path: "token_mint",
                      },
                      {
                        kind: "account",
                        path: "owner",
                      },
                    ],
                  },
                },
                {
                  name: "token_mint",
                  relations: ["token_vault"],
                },
                {
                  name: "vault_token_authority",
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [
                          118, 97, 117, 108, 116, 95, 116, 111, 107, 101, 110,
                          95, 97, 117, 116, 104, 111, 114, 105, 116, 121,
                        ],
                      },
                      {
                        kind: "account",
                        path: "token_vault",
                      },
                    ],
                  },
                },
                {
                  name: "vault_token_account",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "vault_token_authority",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "token_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "owner_token_account",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "owner",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "token_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "owner",
                  writable: !0,
                  signer: !0,
                  relations: ["token_vault"],
                },
                {
                  name: "token_program",
                  address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                },
              ],
              args: [
                {
                  name: "amount",
                  type: "u64",
                },
              ],
            },
            {
              name: "deposit",
              discriminator: [242, 35, 198, 137, 82, 225, 242, 182],
              accounts: [
                {
                  name: "token_vault",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [
                          116, 111, 107, 101, 110, 95, 118, 97, 117, 108, 116,
                        ],
                      },
                      {
                        kind: "account",
                        path: "token_vault.token_mint",
                        account: "TokenVault",
                      },
                      {
                        kind: "account",
                        path: "token_vault.owner",
                        account: "TokenVault",
                      },
                    ],
                  },
                },
                {
                  name: "token_mint",
                  relations: ["token_vault"],
                },
                {
                  name: "vault_token_authority",
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [
                          118, 97, 117, 108, 116, 95, 116, 111, 107, 101, 110,
                          95, 97, 117, 116, 104, 111, 114, 105, 116, 121,
                        ],
                      },
                      {
                        kind: "account",
                        path: "token_vault",
                      },
                    ],
                  },
                },
                {
                  name: "vault_token_account",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "vault_token_authority",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "token_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "signer_token_account",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "signer",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "token_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "signer",
                  writable: !0,
                  signer: !0,
                },
                {
                  name: "vault_owner",
                  writable: !0,
                  signer: !0,
                  optional: !0,
                },
                {
                  name: "token_program",
                  address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                },
              ],
              args: [
                {
                  name: "amount",
                  type: "u64",
                },
              ],
            },
            {
              name: "init_vault",
              discriminator: [77, 79, 85, 150, 33, 217, 52, 106],
              accounts: [
                {
                  name: "token_vault",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [
                          116, 111, 107, 101, 110, 95, 118, 97, 117, 108, 116,
                        ],
                      },
                      {
                        kind: "account",
                        path: "token_mint",
                      },
                      {
                        kind: "account",
                        path: "owner",
                      },
                    ],
                  },
                },
                {
                  name: "token_mint",
                },
                {
                  name: "vault_token_authority",
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [
                          118, 97, 117, 108, 116, 95, 116, 111, 107, 101, 110,
                          95, 97, 117, 116, 104, 111, 114, 105, 116, 121,
                        ],
                      },
                      {
                        kind: "account",
                        path: "token_vault",
                      },
                    ],
                  },
                },
                {
                  name: "vault_token_account",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "vault_token_authority",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "token_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "owner",
                  writable: !0,
                  signer: !0,
                },
                {
                  name: "token_program",
                  address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                },
                {
                  name: "associated_token_program",
                  address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
                },
                {
                  name: "system_program",
                  address: "********************************",
                },
              ],
              args: [
                {
                  name: "requires_owner_signature",
                  type: "bool",
                },
              ],
            },
            {
              name: "remove_funds",
              discriminator: [90, 144, 174, 132, 2, 126, 48, 239],
              accounts: [
                {
                  name: "token_vault",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [
                          116, 111, 107, 101, 110, 95, 118, 97, 117, 108, 116,
                        ],
                      },
                      {
                        kind: "account",
                        path: "token_mint",
                      },
                      {
                        kind: "account",
                        path: "owner",
                      },
                    ],
                  },
                },
                {
                  name: "token_mint",
                  relations: ["token_vault"],
                },
                {
                  name: "vault_token_authority",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [
                          118, 97, 117, 108, 116, 95, 116, 111, 107, 101, 110,
                          95, 97, 117, 116, 104, 111, 114, 105, 116, 121,
                        ],
                      },
                      {
                        kind: "account",
                        path: "token_vault",
                      },
                    ],
                  },
                },
                {
                  name: "vault_token_account",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "vault_token_authority",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "token_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "owner_token_account",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "owner",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "token_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "owner",
                  writable: !0,
                  signer: !0,
                  relations: ["token_vault"],
                },
                {
                  name: "token_program",
                  address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                },
              ],
              args: [
                {
                  name: "amount",
                  type: "u64",
                },
              ],
            },
            {
              name: "withdraw",
              discriminator: [183, 18, 70, 156, 148, 109, 161, 34],
              accounts: [
                {
                  name: "token_vault",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [
                          116, 111, 107, 101, 110, 95, 118, 97, 117, 108, 116,
                        ],
                      },
                      {
                        kind: "account",
                        path: "token_vault.token_mint",
                        account: "TokenVault",
                      },
                      {
                        kind: "account",
                        path: "token_vault.owner",
                        account: "TokenVault",
                      },
                    ],
                  },
                },
                {
                  name: "token_mint",
                  relations: ["token_vault"],
                },
                {
                  name: "vault_token_authority",
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [
                          118, 97, 117, 108, 116, 95, 116, 111, 107, 101, 110,
                          95, 97, 117, 116, 104, 111, 114, 105, 116, 121,
                        ],
                      },
                      {
                        kind: "account",
                        path: "token_vault",
                      },
                    ],
                  },
                },
                {
                  name: "vault_token_account",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "vault_token_authority",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "token_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "signer_token_account",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "signer",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "token_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "signer",
                  writable: !0,
                  signer: !0,
                },
                {
                  name: "vault_owner",
                  writable: !0,
                  signer: !0,
                  optional: !0,
                },
                {
                  name: "token_program",
                  address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                },
                {
                  name: "associated_token_program",
                  address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
                },
                {
                  name: "system_program",
                  address: "********************************",
                },
              ],
              args: [
                {
                  name: "amount",
                  type: "u64",
                },
              ],
            },
          ],
          accounts: [
            {
              name: "TokenVault",
              discriminator: [121, 7, 84, 254, 151, 228, 43, 144],
            },
          ],
          types: [
            {
              name: "TokenVault",
              type: {
                kind: "struct",
                fields: [
                  {
                    name: "owner",
                    docs: ["The owner/staker (32)"],
                    type: "pubkey",
                  },
                  {
                    name: "token_mint",
                    docs: ["The mint of the token (32)"],
                    type: "pubkey",
                  },
                  {
                    name: "vault_bump",
                    docs: ["The bump of Token Vault PDA (1)"],
                    type: "u8",
                  },
                  {
                    name: "token_auth_bump",
                    docs: ["The bump of Token Auth PDA (1)"],
                    type: "u8",
                  },
                  {
                    name: "requires_owner_signature",
                    docs: [
                      "Whether deposit/withdraw requires vault owner signature (1)",
                    ],
                    type: "bool",
                  },
                ],
              },
            },
          ],
        },
        r = {
          address: "EsybJRDoqxAnnBBwjvQ42X1xkgokZEKmSP9Y8sZV7bzN",
          metadata: {
            name: "token_staking_program",
            version: "0.1.0",
            spec: "0.1.0",
            description: "Created with Anchor",
          },
          instructions: [
            {
              name: "add_claimant",
              discriminator: [219, 251, 213, 252, 211, 243, 208, 238],
              accounts: [
                {
                  name: "authority",
                  writable: !0,
                  signer: !0,
                  relations: ["pool"],
                },
                {
                  name: "pool",
                  writable: !0,
                },
                {
                  name: "claimant",
                },
                {
                  name: "claim",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [99, 108, 97, 105, 109],
                      },
                      {
                        kind: "account",
                        path: "pool",
                      },
                      {
                        kind: "account",
                        path: "claimant",
                      },
                    ],
                  },
                },
                {
                  name: "system_program",
                  address: "********************************",
                },
              ],
              args: [
                {
                  name: "amount",
                  type: "u64",
                },
              ],
            },
            {
              name: "claim",
              discriminator: [62, 198, 214, 193, 213, 159, 108, 210],
              accounts: [
                {
                  name: "claimant",
                  writable: !0,
                  signer: !0,
                },
                {
                  name: "pool",
                  writable: !0,
                },
                {
                  name: "claim",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [99, 108, 97, 105, 109],
                      },
                      {
                        kind: "account",
                        path: "pool",
                      },
                      {
                        kind: "account",
                        path: "claimant",
                      },
                    ],
                  },
                },
                {
                  name: "vault",
                  writable: !0,
                },
                {
                  name: "claimant_ata",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "claimant",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "mint",
                },
                {
                  name: "token_program",
                  address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                },
                {
                  name: "associated_token_program",
                  address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
                },
                {
                  name: "system_program",
                  address: "********************************",
                },
              ],
              args: [],
            },
            {
              name: "claim_staking_rewards",
              discriminator: [229, 141, 170, 69, 111, 94, 6, 72],
              accounts: [
                {
                  name: "user",
                  writable: !0,
                  signer: !0,
                },
                {
                  name: "staking_pool",
                  writable: !0,
                },
                {
                  name: "user_stake",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [117, 115, 101, 114, 95, 115, 116, 97, 107, 101],
                      },
                      {
                        kind: "account",
                        path: "staking_pool",
                      },
                      {
                        kind: "account",
                        path: "user",
                      },
                    ],
                  },
                },
                {
                  name: "reward_vault",
                  writable: !0,
                },
                {
                  name: "user_reward_account",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "user",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "reward_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "reward_mint",
                },
                {
                  name: "token_program",
                  address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                },
                {
                  name: "associated_token_program",
                  address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
                },
                {
                  name: "system_program",
                  address: "********************************",
                },
              ],
              args: [],
            },
            {
              name: "claim_to_stake",
              discriminator: [196, 151, 171, 133, 183, 192, 205, 198],
              accounts: [
                {
                  name: "pool",
                  writable: !0,
                },
                {
                  name: "claim_status",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [
                          99, 108, 97, 105, 109, 95, 115, 116, 97, 116, 117,
                          115,
                        ],
                      },
                      {
                        kind: "account",
                        path: "pool",
                      },
                      {
                        kind: "account",
                        path: "claimant",
                      },
                    ],
                  },
                },
                {
                  name: "vault",
                  writable: !0,
                },
                {
                  name: "staking_pool",
                  writable: !0,
                },
                {
                  name: "user_stake",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [117, 115, 101, 114, 95, 115, 116, 97, 107, 101],
                      },
                      {
                        kind: "account",
                        path: "staking_pool",
                      },
                      {
                        kind: "account",
                        path: "claimant",
                      },
                    ],
                  },
                },
                {
                  name: "staking_vault",
                  writable: !0,
                },
                {
                  name: "claimant",
                  writable: !0,
                  signer: !0,
                },
                {
                  name: "token_program",
                  address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                },
                {
                  name: "associated_token_program",
                  address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
                },
                {
                  name: "system_program",
                  address: "********************************",
                },
              ],
              args: [
                {
                  name: "allocation",
                  type: "u64",
                },
                {
                  name: "proof",
                  type: {
                    vec: {
                      array: ["u8", 32],
                    },
                  },
                },
              ],
            },
            {
              name: "claim_with_proof",
              discriminator: [38, 165, 237, 119, 50, 165, 25, 163],
              accounts: [
                {
                  name: "claimant",
                  writable: !0,
                  signer: !0,
                },
                {
                  name: "pool",
                  writable: !0,
                },
                {
                  name: "claim_status",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [
                          99, 108, 97, 105, 109, 95, 115, 116, 97, 116, 117,
                          115,
                        ],
                      },
                      {
                        kind: "account",
                        path: "pool",
                      },
                      {
                        kind: "account",
                        path: "claimant",
                      },
                    ],
                  },
                },
                {
                  name: "vault",
                  writable: !0,
                },
                {
                  name: "claimant_ata",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "claimant",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "mint",
                },
                {
                  name: "token_program",
                  address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                },
                {
                  name: "associated_token_program",
                  address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
                },
                {
                  name: "system_program",
                  address: "********************************",
                },
              ],
              args: [
                {
                  name: "allocation",
                  type: "u64",
                },
                {
                  name: "proof",
                  type: {
                    vec: {
                      array: ["u8", 32],
                    },
                  },
                },
              ],
            },
            {
              name: "close_pool",
              discriminator: [140, 189, 209, 23, 239, 62, 239, 11],
              accounts: [
                {
                  name: "authority",
                  writable: !0,
                  signer: !0,
                  relations: ["pool"],
                },
                {
                  name: "pool",
                  writable: !0,
                },
                {
                  name: "vault",
                  writable: !0,
                },
                {
                  name: "rent_collector",
                  writable: !0,
                },
                {
                  name: "token_program",
                  address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                },
              ],
              args: [],
            },
            {
              name: "complete_unstake",
              discriminator: [79, 98, 40, 241, 100, 30, 25, 234],
              accounts: [
                {
                  name: "user",
                  writable: !0,
                  signer: !0,
                },
                {
                  name: "staking_pool",
                  writable: !0,
                },
                {
                  name: "user_stake",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [117, 115, 101, 114, 95, 115, 116, 97, 107, 101],
                      },
                      {
                        kind: "account",
                        path: "staking_pool",
                      },
                      {
                        kind: "account",
                        path: "user",
                      },
                    ],
                  },
                },
                {
                  name: "staking_vault",
                  writable: !0,
                },
                {
                  name: "user_token_account",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "user",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "staking_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "reward_vault",
                  writable: !0,
                },
                {
                  name: "user_reward_account",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "account",
                        path: "user",
                      },
                      {
                        kind: "const",
                        value: [
                          6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225,
                          70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55,
                          145, 58, 140, 245, 133, 126, 255, 0, 169,
                        ],
                      },
                      {
                        kind: "account",
                        path: "reward_mint",
                      },
                    ],
                    program: {
                      kind: "const",
                      value: [
                        140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41,
                        20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4,
                        142, 123, 216, 219, 233, 248, 89,
                      ],
                    },
                  },
                },
                {
                  name: "staking_mint",
                },
                {
                  name: "reward_mint",
                },
                {
                  name: "token_program",
                  address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                },
                {
                  name: "associated_token_program",
                  address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
                },
                {
                  name: "system_program",
                  address: "********************************",
                },
              ],
              args: [],
            },
            {
              name: "freeze_pool",
              discriminator: [211, 216, 1, 216, 54, 191, 102, 150],
              accounts: [
                {
                  name: "authority",
                  signer: !0,
                  relations: ["pool"],
                },
                {
                  name: "pool",
                  writable: !0,
                },
              ],
              args: [],
            },
            {
              name: "fund_pool",
              discriminator: [36, 57, 233, 176, 181, 20, 87, 159],
              accounts: [
                {
                  name: "authority",
                  writable: !0,
                  signer: !0,
                  relations: ["pool"],
                },
                {
                  name: "pool",
                  writable: !0,
                },
                {
                  name: "authority_token_account",
                  writable: !0,
                },
                {
                  name: "vault",
                  writable: !0,
                },
                {
                  name: "token_program",
                  address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                },
              ],
              args: [
                {
                  name: "amount",
                  type: "u64",
                },
              ],
            },
            {
              name: "fund_rewards",
              discriminator: [114, 64, 163, 112, 175, 167, 19, 121],
              accounts: [
                {
                  name: "authority",
                  writable: !0,
                  signer: !0,
                },
                {
                  name: "staking_pool",
                  writable: !0,
                },
                {
                  name: "reward_vault",
                  writable: !0,
                },
                {
                  name: "authority_token_account",
                  writable: !0,
                },
                {
                  name: "token_program",
                  address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                },
              ],
              args: [
                {
                  name: "amount",
                  type: "u64",
                },
              ],
            },
            {
              name: "initialize_pool",
              discriminator: [95, 180, 10, 172, 84, 174, 232, 40],
              accounts: [
                {
                  name: "authority",
                  writable: !0,
                  signer: !0,
                },
                {
                  name: "mint",
                },
                {
                  name: "pool",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [112, 111, 111, 108],
                      },
                      {
                        kind: "account",
                        path: "mint",
                      },
                      {
                        kind: "account",
                        path: "authority",
                      },
                    ],
                  },
                },
                {
                  name: "vault",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [118, 97, 117, 108, 116],
                      },
                      {
                        kind: "account",
                        path: "pool",
                      },
                    ],
                  },
                },
                {
                  name: "token_program",
                  address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                },
                {
                  name: "system_program",
                  address: "********************************",
                },
              ],
              args: [
                {
                  name: "args",
                  type: {
                    defined: {
                      name: "InitializePoolArgs",
                    },
                  },
                },
              ],
            },
            {
              name: "initialize_staking_pool",
              discriminator: [231, 155, 216, 76, 185, 211, 34, 151],
              accounts: [
                {
                  name: "authority",
                  writable: !0,
                  signer: !0,
                },
                {
                  name: "mint",
                },
                {
                  name: "reward_mint",
                },
                {
                  name: "staking_pool",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [
                          115, 116, 97, 107, 105, 110, 103, 95, 112, 111, 111,
                          108,
                        ],
                      },
                      {
                        kind: "account",
                        path: "mint",
                      },
                      {
                        kind: "account",
                        path: "authority",
                      },
                    ],
                  },
                },
                {
                  name: "staking_vault",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [
                          115, 116, 97, 107, 105, 110, 103, 95, 118, 97, 117,
                          108, 116,
                        ],
                      },
                      {
                        kind: "account",
                        path: "staking_pool",
                      },
                    ],
                  },
                },
                {
                  name: "reward_vault",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [
                          114, 101, 119, 97, 114, 100, 95, 118, 97, 117, 108,
                          116,
                        ],
                      },
                      {
                        kind: "account",
                        path: "staking_pool",
                      },
                    ],
                  },
                },
                {
                  name: "token_program",
                  address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                },
                {
                  name: "system_program",
                  address: "********************************",
                },
              ],
              args: [
                {
                  name: "args",
                  type: {
                    defined: {
                      name: "InitializeStakingPoolArgs",
                    },
                  },
                },
              ],
            },
            {
              name: "request_unlock",
              discriminator: [114, 219, 84, 115, 190, 220, 130, 240],
              accounts: [
                {
                  name: "user",
                  writable: !0,
                  signer: !0,
                },
                {
                  name: "staking_pool",
                  writable: !0,
                },
                {
                  name: "user_stake",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [117, 115, 101, 114, 95, 115, 116, 97, 107, 101],
                      },
                      {
                        kind: "account",
                        path: "staking_pool",
                      },
                      {
                        kind: "account",
                        path: "user",
                      },
                    ],
                  },
                },
              ],
              args: [],
            },
            {
              name: "set_merkle_root",
              discriminator: [43, 24, 91, 60, 240, 137, 28, 102],
              accounts: [
                {
                  name: "authority",
                  signer: !0,
                  relations: ["pool"],
                },
                {
                  name: "pool",
                  writable: !0,
                },
              ],
              args: [
                {
                  name: "merkle_root",
                  type: {
                    array: ["u8", 32],
                  },
                },
                {
                  name: "enable",
                  type: "bool",
                },
              ],
            },
            {
              name: "stake_tokens",
              discriminator: [136, 126, 91, 162, 40, 131, 13, 127],
              accounts: [
                {
                  name: "user",
                  writable: !0,
                  signer: !0,
                },
                {
                  name: "staking_pool",
                  writable: !0,
                },
                {
                  name: "user_stake",
                  writable: !0,
                  pda: {
                    seeds: [
                      {
                        kind: "const",
                        value: [117, 115, 101, 114, 95, 115, 116, 97, 107, 101],
                      },
                      {
                        kind: "account",
                        path: "staking_pool",
                      },
                      {
                        kind: "account",
                        path: "user",
                      },
                    ],
                  },
                },
                {
                  name: "staking_vault",
                  writable: !0,
                },
                {
                  name: "user_token_account",
                  writable: !0,
                },
                {
                  name: "token_program",
                  address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                },
                {
                  name: "associated_token_program",
                  address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
                },
                {
                  name: "system_program",
                  address: "********************************",
                },
              ],
              args: [
                {
                  name: "amount",
                  type: "u64",
                },
              ],
            },
            {
              name: "unfreeze_pool",
              discriminator: [236, 22, 34, 179, 44, 68, 15, 108],
              accounts: [
                {
                  name: "authority",
                  signer: !0,
                  relations: ["pool"],
                },
                {
                  name: "pool",
                  writable: !0,
                },
              ],
              args: [],
            },
            {
              name: "withdraw_unallocated",
              discriminator: [226, 26, 221, 64, 218, 61, 68, 231],
              accounts: [
                {
                  name: "authority",
                  writable: !0,
                  signer: !0,
                  relations: ["pool"],
                },
                {
                  name: "pool",
                  writable: !0,
                },
                {
                  name: "vault",
                  writable: !0,
                },
                {
                  name: "authority_token_account",
                  writable: !0,
                },
                {
                  name: "token_program",
                  address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                },
              ],
              args: [
                {
                  name: "amount",
                  type: "u64",
                },
              ],
            },
          ],
          accounts: [
            {
              name: "Claim",
              discriminator: [155, 70, 22, 176, 123, 215, 246, 102],
            },
            {
              name: "ClaimStatus",
              discriminator: [22, 183, 249, 157, 247, 95, 150, 96],
            },
            {
              name: "Pool",
              discriminator: [241, 154, 109, 4, 17, 177, 109, 188],
            },
            {
              name: "StakingPool",
              discriminator: [203, 19, 214, 220, 220, 154, 24, 102],
            },
            {
              name: "UserStake",
              discriminator: [102, 53, 163, 107, 9, 138, 87, 153],
            },
          ],
          errors: [
            {
              code: 6e3,
              name: "InvalidAmount",
              msg: "Invalid amount",
            },
            {
              code: 6001,
              name: "Overflow",
              msg: "Arithmetic overflow",
            },
            {
              code: 6002,
              name: "ExceedsUnclaimed",
              msg: "Withdraw amount exceeds unclaimed balance",
            },
          ],
          types: [
            {
              name: "Claim",
              type: {
                kind: "struct",
                fields: [
                  {
                    name: "pool",
                    type: "pubkey",
                  },
                  {
                    name: "claimant",
                    type: "pubkey",
                  },
                  {
                    name: "total_allocation",
                    type: "u64",
                  },
                  {
                    name: "claimed_amount",
                    type: "u64",
                  },
                  {
                    name: "bump",
                    type: "u8",
                  },
                ],
              },
            },
            {
              name: "ClaimStatus",
              type: {
                kind: "struct",
                fields: [
                  {
                    name: "pool",
                    type: "pubkey",
                  },
                  {
                    name: "claimant",
                    type: "pubkey",
                  },
                  {
                    name: "claimed_amount",
                    type: "u64",
                  },
                  {
                    name: "bump",
                    type: "u8",
                  },
                ],
              },
            },
            {
              name: "InitializePoolArgs",
              type: {
                kind: "struct",
                fields: [
                  {
                    name: "start_ts",
                    type: "i64",
                  },
                  {
                    name: "end_ts",
                    type: "i64",
                  },
                ],
              },
            },
            {
              name: "InitializeStakingPoolArgs",
              type: {
                kind: "struct",
                fields: [
                  {
                    name: "reward_rate",
                    type: "u64",
                  },
                  {
                    name: "start_time",
                    type: "i64",
                  },
                  {
                    name: "end_time",
                    type: "i64",
                  },
                  {
                    name: "unlock_duration",
                    type: "i64",
                  },
                ],
              },
            },
            {
              name: "Pool",
              type: {
                kind: "struct",
                fields: [
                  {
                    name: "authority",
                    type: "pubkey",
                  },
                  {
                    name: "mint",
                    type: "pubkey",
                  },
                  {
                    name: "vault",
                    type: "pubkey",
                  },
                  {
                    name: "bump",
                    type: "u8",
                  },
                  {
                    name: "is_frozen",
                    type: "bool",
                  },
                  {
                    name: "total_deposited",
                    type: "u64",
                  },
                  {
                    name: "total_claimed",
                    type: "u64",
                  },
                  {
                    name: "start_ts",
                    type: "i64",
                  },
                  {
                    name: "end_ts",
                    type: "i64",
                  },
                  {
                    name: "merkle_root",
                    type: {
                      array: ["u8", 32],
                    },
                  },
                  {
                    name: "merkle_enabled",
                    type: "bool",
                  },
                ],
              },
            },
            {
              name: "StakingPool",
              type: {
                kind: "struct",
                fields: [
                  {
                    name: "authority",
                    type: "pubkey",
                  },
                  {
                    name: "mint",
                    type: "pubkey",
                  },
                  {
                    name: "reward_mint",
                    type: "pubkey",
                  },
                  {
                    name: "staking_vault",
                    type: "pubkey",
                  },
                  {
                    name: "reward_vault",
                    type: "pubkey",
                  },
                  {
                    name: "bump",
                    type: "u8",
                  },
                  {
                    name: "is_active",
                    type: "bool",
                  },
                  {
                    name: "total_staked",
                    type: "u64",
                  },
                  {
                    name: "total_rewards_distributed",
                    type: "u64",
                  },
                  {
                    name: "reward_rate",
                    type: "u64",
                  },
                  {
                    name: "start_time",
                    type: "i64",
                  },
                  {
                    name: "end_time",
                    type: "i64",
                  },
                  {
                    name: "unlock_duration",
                    type: "i64",
                  },
                ],
              },
            },
            {
              name: "UserStake",
              type: {
                kind: "struct",
                fields: [
                  {
                    name: "staking_pool",
                    type: "pubkey",
                  },
                  {
                    name: "user",
                    type: "pubkey",
                  },
                  {
                    name: "staked_amount",
                    type: "u64",
                  },
                  {
                    name: "staked_at",
                    type: "i64",
                  },
                  {
                    name: "last_claim_ts",
                    type: "i64",
                  },
                  {
                    name: "pending_rewards",
                    type: "u64",
                  },
                  {
                    name: "unlock_requested_at",
                    type: "i64",
                  },
                  {
                    name: "bump",
                    type: "u8",
                  },
                ],
              },
            },
          ],
        },
        o = 1e3,
        s = ["Backpack"],
        d = [
          {
            level: 1,
            freePrizes: [
              {
                prizeLevel: 1,
                type: "chest",
                id: 2e3,
                quantity: 1,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 101,
                type: "chest",
                id: 2001,
                quantity: 1,
              },
            ],
          },
          {
            level: 2,
            freePrizes: [
              {
                prizeLevel: 2,
                type: "material",
                id: 5104,
                quantity: 2,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 102,
                type: "c2",
                id: "c2",
                quantity: 5e4,
              },
            ],
          },
          {
            level: 3,
            freePrizes: [
              {
                prizeLevel: 3,
                type: "material",
                id: 5e3,
                quantity: 10,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 103,
                type: "material",
                id: 5e3,
                quantity: 10,
              },
            ],
          },
          {
            level: 4,
            premPrizes: [
              {
                prizeLevel: 104,
                type: "material",
                id: 5101,
                quantity: 10,
              },
            ],
          },
          {
            level: 5,
            freePrizes: [
              {
                prizeLevel: 5,
                type: "material",
                id: 5001,
                quantity: 10,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 105,
                type: "c2",
                id: "c2",
                quantity: 5e4,
              },
            ],
          },
          {
            level: 6,
            premPrizes: [
              {
                prizeLevel: 106,
                type: "material",
                id: 5001,
                quantity: 10,
              },
            ],
          },
          {
            level: 7,
            freePrizes: [
              {
                prizeLevel: 7,
                type: "material",
                id: 5100,
                quantity: 20,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 107,
                type: "material",
                id: 5104,
                quantity: 4,
              },
            ],
          },
          {
            level: 8,
            freePrizes: [
              {
                prizeLevel: 8,
                type: "material",
                id: 5109,
                quantity: 1,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 108,
                type: "c2",
                id: "c2",
                quantity: 5e4,
              },
            ],
          },
          {
            level: 9,
            freePrizes: [
              {
                prizeLevel: 9,
                type: "material",
                id: 5003,
                quantity: 10,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 109,
                type: "material",
                id: 5003,
                quantity: 10,
              },
            ],
          },
          {
            level: 10,
            premPrizes: [
              {
                prizeLevel: 110,
                type: "material",
                id: 5109,
                quantity: 2,
              },
            ],
          },
          {
            level: 11,
            freePrizes: [
              {
                prizeLevel: 11,
                type: "material",
                id: 5101,
                quantity: 10,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 111,
                type: "c2",
                id: "c2",
                quantity: 5e4,
              },
            ],
          },
          {
            level: 12,
            freePrizes: [
              {
                prizeLevel: 12,
                type: "material",
                id: 5104,
                quantity: 2,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 112,
                type: "material",
                id: 5107,
                quantity: 2,
              },
            ],
          },
          {
            level: 13,
            freePrizes: [
              {
                prizeLevel: 13,
                type: "material",
                id: 5005,
                quantity: 3,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 113,
                type: "material",
                id: 5102,
                quantity: 20,
              },
            ],
          },
          {
            level: 14,
            premPrizes: [
              {
                prizeLevel: 114,
                type: "chest",
                id: 2e3,
                quantity: 1,
              },
            ],
          },
          {
            level: 15,
            freePrizes: [
              {
                prizeLevel: 15,
                type: "c2",
                id: "c2",
                quantity: 5e4,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 115,
                type: "c1",
                id: "c1",
                quantity: 150,
              },
            ],
          },
          {
            level: 16,
            premPrizes: [
              {
                prizeLevel: 116,
                type: "material",
                id: 5004,
                quantity: 6,
              },
            ],
          },
          {
            level: 17,
            freePrizes: [
              {
                prizeLevel: 17,
                type: "material",
                id: 5002,
                quantity: 3,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 117,
                type: "material",
                id: 5104,
                quantity: 4,
              },
            ],
          },
          {
            level: 18,
            freePrizes: [
              {
                prizeLevel: 18,
                type: "material",
                id: 5109,
                quantity: 1,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 118,
                type: "chest",
                id: 2e3,
                quantity: 1,
              },
            ],
          },
          {
            level: 19,
            premPrizes: [
              {
                prizeLevel: 119,
                type: "material",
                id: 5105,
                quantity: 1,
              },
            ],
          },
          {
            level: 20,
            freePrizes: [
              {
                prizeLevel: 20,
                type: "item",
                id: 4070,
                quantity: 1,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 120,
                type: "c2",
                id: "c2",
                quantity: 5e5,
              },
            ],
          },
          {
            level: 21,
            freePrizes: [
              {
                prizeLevel: 21,
                type: "material",
                id: 5102,
                quantity: 5,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 121,
                type: "material",
                id: 5108,
                quantity: 1,
              },
            ],
          },
          {
            level: 22,
            freePrizes: [
              {
                prizeLevel: 22,
                type: "material",
                id: 5104,
                quantity: 2,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 122,
                type: "material",
                id: 5103,
                quantity: 12,
              },
            ],
          },
          {
            level: 23,
            freePrizes: [
              {
                prizeLevel: 23,
                type: "material",
                id: 5107,
                quantity: 1,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 123,
                type: "c2",
                id: "c2",
                quantity: 25e4,
              },
            ],
          },
          {
            level: 24,
            premPrizes: [
              {
                prizeLevel: 124,
                type: "material",
                id: 5007,
                quantity: 3,
              },
            ],
          },
          {
            level: 25,
            freePrizes: [
              {
                prizeLevel: 25,
                type: "c2",
                id: "c2",
                quantity: 3e5,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 125,
                type: "c1",
                id: "c1",
                quantity: 750,
              },
            ],
          },
          {
            level: 26,
            premPrizes: [
              {
                prizeLevel: 126,
                type: "material",
                id: 5102,
                quantity: 20,
              },
            ],
          },
          {
            level: 27,
            freePrizes: [
              {
                prizeLevel: 27,
                type: "material",
                id: 5004,
                quantity: 3,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 127,
                type: "material",
                id: 5107,
                quantity: 2,
              },
            ],
          },
          {
            level: 28,
            premPrizes: [
              {
                prizeLevel: 128,
                type: "chest",
                id: 2e3,
                quantity: 1,
              },
            ],
          },
          {
            level: 29,
            premPrizes: [
              {
                prizeLevel: 129,
                type: "c2",
                id: "c2",
                quantity: 25e4,
              },
            ],
          },
          {
            level: 30,
            freePrizes: [
              {
                prizeLevel: 30,
                type: "item",
                id: 4073,
                quantity: 1,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 130,
                type: "chest",
                id: 2001,
                quantity: 1,
              },
            ],
          },
          {
            level: 31,
            premPrizes: [
              {
                prizeLevel: 131,
                type: "material",
                id: 5104,
                quantity: 4,
              },
            ],
          },
          {
            level: 32,
            freePrizes: [
              {
                prizeLevel: 32,
                type: "material",
                id: 5104,
                quantity: 2,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 132,
                type: "material",
                id: 5103,
                quantity: 12,
              },
            ],
          },
          {
            level: 33,
            freePrizes: [
              {
                prizeLevel: 33,
                type: "material",
                id: 5006,
                quantity: 1,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 133,
                type: "c2",
                id: "c2",
                quantity: 3e5,
              },
            ],
          },
          {
            level: 34,
            premPrizes: [
              {
                prizeLevel: 134,
                type: "material",
                id: 5002,
                quantity: 6,
              },
            ],
          },
          {
            level: 35,
            freePrizes: [
              {
                prizeLevel: 35,
                type: "c1",
                id: "c1",
                quantity: 150,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 135,
                type: "item",
                id: 4010,
                quantity: 1,
              },
            ],
          },
          {
            level: 36,
            premPrizes: [
              {
                prizeLevel: 136,
                type: "material",
                id: 5109,
                quantity: 2,
              },
            ],
          },
          {
            level: 37,
            freePrizes: [
              {
                prizeLevel: 37,
                type: "material",
                id: 5007,
                quantity: 1,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 137,
                type: "material",
                id: 5005,
                quantity: 6,
              },
            ],
          },
          {
            level: 38,
            freePrizes: [
              {
                prizeLevel: 38,
                type: "material",
                id: 5109,
                quantity: 1,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 138,
                type: "material",
                id: 5103,
                quantity: 12,
              },
            ],
          },
          {
            level: 39,
            premPrizes: [
              {
                prizeLevel: 139,
                type: "c1",
                id: "c1",
                quantity: 200,
              },
            ],
          },
          {
            level: 40,
            freePrizes: [
              {
                prizeLevel: 40,
                type: "c2",
                id: "c2",
                quantity: 5e4,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 140,
                type: "material",
                id: 5006,
                quantity: 3,
              },
            ],
          },
          {
            level: 41,
            freePrizes: [
              {
                prizeLevel: 41,
                type: "material",
                id: 5103,
                quantity: 5,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 141,
                type: "material",
                id: 5102,
                quantity: 25,
              },
            ],
          },
          {
            level: 42,
            freePrizes: [
              {
                prizeLevel: 42,
                type: "material",
                id: 5104,
                quantity: 2,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 142,
                type: "material",
                id: 5107,
                quantity: 2,
              },
            ],
          },
          {
            level: 43,
            freePrizes: [
              {
                prizeLevel: 43,
                type: "material",
                id: 5107,
                quantity: 1,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 143,
                type: "material",
                id: 5103,
                quantity: 12,
              },
            ],
          },
          {
            level: 44,
            premPrizes: [
              {
                prizeLevel: 144,
                type: "c2",
                id: "c2",
                quantity: 3e5,
              },
            ],
          },
          {
            level: 45,
            freePrizes: [
              {
                prizeLevel: 45,
                type: "item",
                id: 4076,
                quantity: 1,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 145,
                type: "item",
                id: 4012,
                quantity: 1,
              },
            ],
          },
          {
            level: 46,
            premPrizes: [
              {
                prizeLevel: 146,
                type: "material",
                id: 5008,
                quantity: 3,
              },
            ],
          },
          {
            level: 47,
            freePrizes: [
              {
                prizeLevel: 47,
                type: "material",
                id: 5008,
                quantity: 1,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 147,
                type: "material",
                id: 5102,
                quantity: 25,
              },
            ],
          },
          {
            level: 48,
            premPrizes: [
              {
                prizeLevel: 148,
                type: "material",
                id: 5104,
                quantity: 4,
              },
            ],
          },
          {
            level: 49,
            freePrizes: [
              {
                prizeLevel: 49,
                type: "material",
                id: 5006,
                quantity: 2,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 149,
                type: "material",
                id: 5109,
                quantity: 2,
              },
            ],
          },
          {
            level: 50,
            freePrizes: [
              {
                prizeLevel: 50,
                type: "pet",
                id: 7003,
                quantity: 1,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 150,
                type: "pet",
                id: 7021,
                quantity: 1,
              },
            ],
          },
          {
            level: 51,
            premPrizes: [
              {
                prizeLevel: 151,
                type: "material",
                id: 5105,
                quantity: 1,
              },
            ],
          },
          {
            level: 52,
            freePrizes: [
              {
                prizeLevel: 52,
                type: "chest",
                id: 2e3,
                quantity: 1,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 152,
                type: "material",
                id: 5110,
                quantity: 1,
              },
            ],
          },
          {
            level: 53,
            freePrizes: [
              {
                prizeLevel: 53,
                type: "material",
                id: 5107,
                quantity: 1,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 153,
                type: "c2",
                id: "c2",
                quantity: 3e5,
              },
            ],
          },
          {
            level: 54,
            freePrizes: [
              {
                prizeLevel: 54,
                type: "material",
                id: 5105,
                quantity: 1,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 154,
                type: "material",
                id: 5108,
                quantity: 1,
              },
            ],
          },
          {
            level: 55,
            freePrizes: [
              {
                prizeLevel: 55,
                type: "c2",
                id: "c2",
                quantity: 75e4,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 155,
                type: "item",
                id: 4014,
                quantity: 1,
              },
            ],
          },
          {
            level: 56,
            premPrizes: [
              {
                prizeLevel: 156,
                type: "material",
                id: 5103,
                quantity: 12,
              },
            ],
          },
          {
            level: 57,
            freePrizes: [
              {
                prizeLevel: 57,
                type: "material",
                id: 5103,
                quantity: 10,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 157,
                type: "material",
                id: 5107,
                quantity: 2,
              },
            ],
          },
          {
            level: 58,
            freePrizes: [
              {
                prizeLevel: 58,
                type: "material",
                id: 5109,
                quantity: 1,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 158,
                type: "chest",
                id: 2e3,
                quantity: 1,
              },
            ],
          },
          {
            level: 59,
            premPrizes: [
              {
                prizeLevel: 159,
                type: "material",
                id: 5104,
                quantity: 4,
              },
            ],
          },
          {
            level: 60,
            freePrizes: [
              {
                prizeLevel: 60,
                type: "chest",
                id: 2001,
                quantity: 1,
              },
            ],
            premPrizes: [
              {
                prizeLevel: 160,
                type: "chest",
                id: 2002,
                quantity: 1,
              },
            ],
          },
        ],
        l = new Date(17574336e5);
      var c = "LEAFqNixpTuk8UCfrxvs2r3MQRB5xEh6Gw9YjMTt6WB",
        m = 432e5;
    },
    88902: (e, a, t) => {
      "use strict";
      t.d(a, {
        $: () => d,
      });
      var n = t(54568);
      t(7620);
      var i = t(60607);
      let r = [
          "relative inline-block items-center justify-center select-none",
          "border-2",
          "uppercase",
          "disabled:opacity-50 transition-all",
          'after:content-[""] after:absolute after:box-border',
          "after:top-0 after:right-0 after:bottom-0 after:left-0",
          "disabled:cursor-not-allowed",
          "focus:outline-none",
        ],
        o = {
          small: "text-xs px-3 py-1.5",
          medium: "px-4 py-2",
          inline: "p-0",
        },
        s = {
          normal: (0, i.QP)(
            "bg-bg-accent-2 text-white enabled:hover:bg-bg-accent-1 enabled:focus:bg-bg-accent-1",
            "after:shadow-[inset_-4px_-4px_#323e34]",
            "enabled:after:hover:shadow-[inset_-5px_-5px_#323e34]",
            "enabled:after:active:shadow-[inset_5px_5px_#323e34]"
          ),
          primary: (0, i.QP)(
            "bg-primary-accent-1 text-black font-bold enabled:hover:bg-primary-accent-2 enabled:focus:bg-primary-accent-2",
            "after:shadow-[inset_-4px_-4px_#0eb057]",
            "enabled:after:hover:shadow-[inset_-5px_-5px_#0eb057]",
            "enabled:after:active:shadow-[inset_5px_5px_#0eb057]"
          ),
          success: (0, i.QP)(
            "bg-success-accent-1 text-black font-bold enabled:hover:bg-success-accent-2 enabled:focus:bg-success-accent-2",
            "after:shadow-[inset_-4px_-4px_#4aa52e]",
            "enabled:after:hover:shadow-[inset_-5px_-5px_#4aa52e]",
            "enabled:after:active:shadow-[inset_5px_5px_#4aa52e]"
          ),
          error: (0, i.QP)(
            "bg-error-accent-1 text-white enabled:hover:bg-error-accent-2 enabled:focus:bg-error-accent-2",
            "after:shadow-[inset_-4px_-4px_#8c2022]",
            "enabled:after:hover:shadow-[inset_-5px_-5px_#8c2022]",
            "enabled:after:active:shadow-[inset_5px_5px_#8c2022]"
          ),
          "error-outline": (0, i.QP)(
            "bg-bg-accent-2 text-white enabled:hover:bg-bg-accent-1 enabled:focus:bg-bg-accent-1",
            "border-error-accent-1 text-error-accent-1",
            "after:shadow-[inset_-4px_-4px_#2F1B15]",
            "enabled:after:hover:shadow-[inset_-5px_-5px_#2F1B15]",
            "enabled:after:active:shadow-[inset_5px_5px_#2F1B15]"
          ),
          select: "justify-between bg-zinc-100 border-0 px-3 font-normal",
          "warning-outline":
            "bg-white border-red-300 text-red-500 enabled:hover:bg-red-50",
          "warning-solid":
            "bg-red-500 border-red-500 text-white enabled:hover:bg-red-700 enabled:hover:border-red-700",
          discord: (0, i.QP)(
            "bg-discord-accent-1 text-white enabled:hover:bg-discord-accent-2 enabled:focus:bg-discord-accent-2",
            "after:shadow-[inset_-4px_-4px_#3542EC]",
            "enabled:after:hover:shadow-[inset_-5px_-5px_#3542EC]",
            "enabled:after:active:shadow-[inset_5px_5px_#3542EC]"
          ),
          twitter: (0, i.QP)(
            "bg-black text-white enabled:hover:bg-neutral-900 enabled:focus:bg-neutral-900",
            "after:shadow-[inset_-4px_-4px_#444444]",
            "enabled:after:hover:shadow-[inset_-5px_-5px_#444444]",
            "enabled:after:active:shadow-[inset_5px_5px_#444444]"
          ),
          inline: (0, i.QP)(
            "text-primary-accent-1 hover:drop-shadow-primary-accent transition-all",
            "border-0 bg-transparent normal-case"
          ),
          "inline-error": (0, i.QP)(
            "text-error-accent-1 hover:drop-shadow-error-accent transition-all",
            "border-0 bg-transparent normal-case"
          ),
        },
        d = (e) => {
          let {
            ref: a,
            children: t,
            variant: d = "normal",
            size: l = "medium",
            ...c
          } = e;
          return (0, n.jsx)("button", {
            ref: a,
            ...c,
            className: (0, i.QP)(r, o[l], s[d], c.className),
            children: t,
          });
        };
      d.displayName = "Button";
    },
  },
]);
