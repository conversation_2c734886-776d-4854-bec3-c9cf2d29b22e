(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
  [2544],
  {
    1185: (t, e, a) => {
      Promise.resolve().then(a.bind(a, 30662)),
        Promise.resolve().then(a.t.bind(a, 73970, 23));
    },
    8321: (t, e, a) => {
      "use strict";
      a.d(e, {
        S: () => o,
      });
      var s = a(78324),
        i = a(32708),
        r = a(4020),
        n = a(47380);
      function o() {
        let { connection: t } = (0, i.w)(),
          e = (0, r.M)(),
          a = new s.NQ(t, e, {
            commitment: "confirmed",
          });
        return (
          (0, s.U0)(a),
          {
            program: new s.BP(n.g_, a),
          }
        );
      }
    },
    16258: (t, e, a) => {
      "use strict";
      a.d(e, {
        useMutation: () => h,
      });
      var s = a(7620),
        i = a(80589),
        r = a(60494),
        n = a(72327),
        o = a(7703),
        u = class extends n.Q {
          #t;
          #e = void 0;
          #a;
          #s;
          constructor(t, e) {
            super(),
              (this.#t = t),
              this.setOptions(e),
              this.bindMethods(),
              this.#i();
          }
          bindMethods() {
            (this.mutate = this.mutate.bind(this)),
              (this.reset = this.reset.bind(this));
          }
          setOptions(t) {
            let e = this.options;
            (this.options = this.#t.defaultMutationOptions(t)),
              (0, o.f8)(this.options, e) ||
                this.#t.getMutationCache().notify({
                  type: "observerOptionsUpdated",
                  mutation: this.#a,
                  observer: this,
                }),
              e?.mutationKey &&
              this.options.mutationKey &&
              (0, o.EN)(e.mutationKey) !== (0, o.EN)(this.options.mutationKey)
                ? this.reset()
                : this.#a?.state.status === "pending" &&
                  this.#a.setOptions(this.options);
          }
          onUnsubscribe() {
            this.hasListeners() || this.#a?.removeObserver(this);
          }
          onMutationUpdate(t) {
            this.#i(), this.#r(t);
          }
          getCurrentResult() {
            return this.#e;
          }
          reset() {
            this.#a?.removeObserver(this),
              (this.#a = void 0),
              this.#i(),
              this.#r();
          }
          mutate(t, e) {
            return (
              (this.#s = e),
              this.#a?.removeObserver(this),
              (this.#a = this.#t
                .getMutationCache()
                .build(this.#t, this.options)),
              this.#a.addObserver(this),
              this.#a.execute(t)
            );
          }
          #i() {
            let t = this.#a?.state ?? (0, i.$)();
            this.#e = {
              ...t,
              isPending: "pending" === t.status,
              isSuccess: "success" === t.status,
              isError: "error" === t.status,
              isIdle: "idle" === t.status,
              mutate: this.mutate,
              reset: this.reset,
            };
          }
          #r(t) {
            r.j.batch(() => {
              if (this.#s && this.hasListeners()) {
                let e = this.#e.variables,
                  a = this.#e.context;
                t?.type === "success"
                  ? (this.#s.onSuccess?.(t.data, e, a),
                    this.#s.onSettled?.(t.data, null, e, a))
                  : t?.type === "error" &&
                    (this.#s.onError?.(t.error, e, a),
                    this.#s.onSettled?.(void 0, t.error, e, a));
              }
              this.listeners.forEach((t) => {
                t(this.#e);
              });
            });
          }
        },
        l = a(87606),
        c = a(83777);
      function h(t, e) {
        let a = (0, l.useQueryClient)(e),
          [i] = s.useState(() => new u(a, t));
        s.useEffect(() => {
          i.setOptions(t);
        }, [i, t]);
        let n = s.useSyncExternalStore(
            s.useCallback((t) => i.subscribe(r.j.batchCalls(t)), [i]),
            () => i.getCurrentResult(),
            () => i.getCurrentResult()
          ),
          o = s.useCallback(
            (t, e) => {
              i.mutate(t, e).catch(c.l);
            },
            [i]
          );
        if (n.error && (0, c.G)(i.options.throwOnError, [n.error]))
          throw n.error;
        return {
          ...n,
          mutate: o,
          mutateAsync: n.mutate,
        };
      }
    },
    30662: (t, e, a) => {
      "use strict";
      a.d(e, {
        ClaimControl: () => E,
      });
      var s = a(54568),
        i = a(67933),
        r = a(89329),
        n = a(69715),
        o = a(27261),
        u = a.n(o);
      let l = {
          closed_alpha_A: "Closed Alpha",
          closed_alpha_B: "Closed Alpha",
          closed_alpha_C: "Closed Alpha",
          alpha_A: "Open Alpha",
          alpha_B: "Open Alpha",
          alpha_C: "Open Alpha",
          alpha_D: "Open Alpha",
          alpha_E: "Open Alpha",
          alpha_F: "Open Alpha",
          alpha_G: "Open Alpha",
          alpha_H: "Open Alpha",
          alpha_I: "Open Alpha",
          beta_A: "Beta",
          beta_B: "Beta",
          beta_C: "Beta",
          beta_D: "Beta",
          beta_E: "Beta",
          beta_F: "Beta",
          season_0_A: "LEAF Season",
          season_0_B: "LEAF Season",
          season_0_C: "LEAF Season",
          season_0_D: "LEAF Season",
          tx_A: "High Spender",
          tx_B: "High Spender",
          tx_C: "High Spender",
          week1: "Weekly Competition Week 1",
          week2: "Weekly Competition Week 2",
          week3: "Weekly Competition Week 3",
          week4: "Weekly Competition Week 4",
          week5: "Weekly Competition Week 5",
          week6: "Weekly Competition Week 6",
          survivor: "Survivor",
          genesis_user: "Genesis User",
          holder: "NFT Holder",
          community: "Community Events",
          hero_master: "Hero Master",
          collector: "Collector",
          dks: "DKS Exclusive",
        },
        c = (t) => {
          let { error: e, allocations: a } = t;
          return e || !a
            ? (0, s.jsx)("div", {
                children: "Your wallet is not eligible for this claim",
              })
            : (0, s.jsxs)("div", {
                className: "flex flex-col gap-2",
                children: [
                  (0, s.jsxs)("p", {
                    children: [
                      "You are eligible for",
                      " ",
                      (0, s.jsxs)("span", {
                        className: "text-primary-accent-1",
                        children: [
                          null == a
                            ? void 0
                            : a
                                .reduce((t, e) => t + e.allocation, 0)
                                .toLocaleString(),
                          " ",
                          "$LEAF",
                        ],
                      }),
                    ],
                  }),
                  (0, s.jsx)("p", {
                    className: "text-text-accent-1 text-sm",
                    children: "Detailed Breakdown",
                  }),
                  a.map((t) =>
                    (0, s.jsxs)(
                      "p",
                      {
                        className: "text-xs",
                        children: [
                          (0, s.jsx)("span", {
                            className: "text-text-accent-1",
                            children: l[t.category],
                          }),
                          " ",
                          t.allocation.toLocaleString(),
                        ],
                      },
                      "".concat(t.userId, "-").concat(t.category)
                    )
                  ),
                  (0, s.jsxs)("p", {
                    children: [
                      "Note: after claiming, your allocation will be automatically staked into the staking vault to earn rewards. You can manage your staked tokens on the Tokens page under Assets.",
                      " ",
                      (0, s.jsxs)(u(), {
                        href: "/assets/token",
                        className:
                          "text-primary-accent-1 hover:drop-shadow-primary-accent",
                        children: ["Click Here ", ">"],
                      }),
                    ],
                  }),
                ],
              });
        };
      var h = a(78324),
        d = a(53669),
        p = a(32708),
        m = a(34985),
        f = a(87606),
        y = a(60844),
        g = a(7620),
        v = a(63237),
        w = a(47380),
        b = a(55504),
        C = a(88902),
        k = a(72694),
        x = a(8321),
        S = a(93051),
        A = a(51389);
      function P(t) {
        let { claimStatus: e } = t;
        return (0, s.jsxs)("div", {
          children: [
            "You have successfully claimed your LEAF. You can head to staking section to view and manage your staked $LEAF.",
            " ",
            (0, s.jsxs)(u(), {
              href: "/assets/token",
              className:
                "text-primary-accent-1 hover:drop-shadow-primary-accent",
              children: ["Click Here ", ">"],
            }),
          ],
        });
      }
      var M = a(50887).Buffer;
      let _ = async (t, e, a) => {
          try {
            let s = m.PublicKey.findProgramAddressSync(
              [M.from("claim_status"), t.toBuffer(), e.toBuffer()],
              a.programId
            )[0];
            return await a.account.claimStatus.fetch(s);
          } catch (t) {
            return null;
          }
        },
        R = (t) => {
          if (t <= 0) return "00:00:00:00";
          let e = Math.floor(t / 864e5),
            a = Math.floor((t % 864e5) / 36e5),
            s = Math.floor((t % 36e5) / 6e4),
            i = Math.floor((t % 6e4) / 1e3);
          return ""
            .concat(e.toString().padStart(2, "0"), ":")
            .concat(a.toString().padStart(2, "0"), ":")
            .concat(s.toString().padStart(2, "0"), ":")
            .concat(i.toString().padStart(2, "0"));
        };
      function B() {
        let {
            publicKey: t,
            signTransaction: e,
            sendTransaction: a,
          } = (0, d.v)(),
          { connection: n } = (0, p.w)(),
          { program: o } = (0, x.S)(),
          u = (0, y.md)(b.JY),
          [l, c] = (0, g.useState)(!1),
          [M, B] = (0, g.useState)(0),
          [E, O] = (0, g.useState)(!1),
          N = (0, f.useQueryClient)();
        (0, g.useEffect)(() => {
          let t = () => {
            let t = new Date().getTime(),
              e = w.gV.getTime() - t;
            e <= 0 ? (B(0), O(!0)) : (B(e), O(!1));
          };
          t();
          let e = setInterval(t, 500);
          return () => clearInterval(e);
        }, []);
        let j = (0, g.useMemo)(
            () => (0, S.n)(new m.PublicKey(w.SR), new m.PublicKey(k.l9), o),
            [o]
          ),
          F = j.pool,
          K = j.vault,
          T = (0, g.useMemo)(
            () =>
              (0, S.v)(
                new m.PublicKey(w.SR),
                new m.PublicKey(w.SR),
                new m.PublicKey(k.l9),
                o
              ),
            [o]
          ),
          L = T.stakingPool,
          G = T.stakingVault;
        T.rewardVault;
        let { data: U, status: D } = (0, i.useQuery)({
            enabled: !!t,
            retry: !1,
            queryKey: ["claim-status"],
            queryFn: async () => await _(F, t, o),
          }),
          W = (0, g.useCallback)(async () => {
            if (!t || !e) throw Error("Wallet not connected!");
            c(!0);
            let a = (
                await r.A.get(
                  "".concat("https://launcher.backwoods.gg", "/v1/claim/proof"),
                  {
                    withCredentials: !0,
                  }
                )
              ).data.map((t) => t.data),
              s = (
                await r.A.get(
                  "".concat("https://launcher.backwoods.gg", "/v1/claim"),
                  {
                    withCredentials: !0,
                  }
                )
              ).data.data.allocation,
              i = await o.methods
                .claimToStake(new h.BN(s).mul(new h.BN(10).pow(new h.BN(9))), a)
                .accounts({
                  pool: F,
                  vault: K,
                  stakingPool: L,
                  stakingVault: G,
                  claimant: t,
                })
                .instruction(),
              { modifyComputeUnits: l, modifyComputePrice: d } = await (0, A.b)(
                n,
                [i],
                t,
                u
              ),
              { blockhash: p } = await n.getLatestBlockhash("confirmed"),
              f = new m.TransactionMessage({
                payerKey: t,
                recentBlockhash: p,
                instructions: [l, d, i],
              }).compileToV0Message(),
              y = new m.VersionedTransaction(f),
              g = (await e(y)).serialize(),
              v = await n.sendRawTransaction(g, {
                skipPreflight: !0,
                maxRetries: 2,
              }),
              w = await n.getLatestBlockhash();
            await n.confirmTransaction({
              blockhash: w.blockhash,
              lastValidBlockHeight: w.lastValidBlockHeight,
              signature: v,
            }),
              c(!1);
          }, [o, t, e, a, n]);
        return "pending" === D
          ? (0, s.jsx)("div", {
              children: "Pending...",
            })
          : "error" === D
          ? (0, s.jsx)("div", {
              children: "Error",
            })
          : (0, s.jsxs)(s.Fragment, {
              children: [
                !U &&
                  (0, s.jsxs)("div", {
                    className: "flex flex-col items-start gap-2",
                    children: [
                      !E &&
                        (0, s.jsxs)("div", {
                          className: "text-start",
                          children: [
                            (0, s.jsx)("p", {
                              className: "text-sm text-text-accent-1 mb-1",
                              children: "Claim starts in:",
                            }),
                            (0, s.jsx)("p", {
                              children: R(M),
                            }),
                          ],
                        }),
                      (0, s.jsx)(C.$, {
                        variant: "primary",
                        disabled: l || !E,
                        onClick: () =>
                          v.Ay.promise(W(), {
                            loading: "Claiming...",
                            success: () => (
                              N.setQueryData(["claim-status"], {
                                pool: new m.PublicKey(w.SR),
                                claimant: t,
                                claimedAmount: new h.BN(1),
                                bump: 255,
                              }),
                              "Claimed!"
                            ),
                            error: (t) => (c(!1), t.message),
                          }),
                        children: E ? "Claim" : "Claim Not Available",
                      }),
                    ],
                  }),
                U &&
                  (0, s.jsx)(P, {
                    claimStatus: U,
                  }),
              ],
            });
      }
      function E() {
        var t, e;
        let a = (0, n.A)(),
          { data: o, status: u } = (0, i.useQuery)({
            queryKey: ["claim-allocation"],
            queryFn: () =>
              r.A.get("".concat("https://launcher.backwoods.gg", "/v1/claim"), {
                withCredentials: !0,
              }),
          });
        return (null == (t = a.data) ? void 0 : t.isAuthenticated)
          ? (null == o ? void 0 : o.data)
            ? (0, s.jsxs)("div", {
                className: "flex flex-col gap-4 items-start",
                children: [
                  (0, s.jsx)(c, {
                    error: o.data.error,
                    allocations: o.data.data,
                  }),
                  (null == (e = o.data.data) ? void 0 : e.length) > 0 &&
                    (0, s.jsx)(B, {}),
                ],
              })
            : (0, s.jsx)("div", {
                children: "There was an error getting your allocation",
              })
          : (0, s.jsx)("div", {
              children: "Please Sign In First",
            });
      }
    },
    51389: (t, e, a) => {
      "use strict";
      a.d(e, {
        b: () => r,
      });
      var s = a(34985);
      let i = async (t, e, a, i) => {
          var r, n;
          let o = [
              s.ComputeBudgetProgram.setComputeUnitLimit({
                units: 14e5,
              }),
              ...e,
            ],
            u = new s.VersionedTransaction(
              new s.TransactionMessage({
                instructions: o,
                payerKey: a,
                recentBlockhash: s.PublicKey.default.toString(),
              }).compileToV0Message(i)
            ),
            l = await t.simulateTransaction(u, {
              replaceRecentBlockhash: !0,
              sigVerify: !1,
            });
          if (null == l || null == (r = l.value) ? void 0 : r.err) {
            let t =
              (null == (n = l.value.logs) ? void 0 : n.join("\n  • ")) ||
              "No logs available";
            throw Error("Transaction simulation failed:\n  •".concat(t));
          }
          return l.value.unitsConsumed || null;
        },
        r = async (t, e, a, r) => {
          let n,
            o = e.flatMap((t) => t.keys.map((t) => t.pubkey)),
            u = await i(t, [...e], a, []),
            l = u ? Math.floor(1.1 * u) : 3e5,
            c = s.ComputeBudgetProgram.setComputeUnitLimit({
              units: u ? Math.floor(1.1 * u) : 3e5,
            });
          if (r.auto) {
            let e = (
                await t.getRecentPrioritizationFees({
                  lockedWritableAccounts: o,
                })
              )
                .map((t) => t.prioritizationFee)
                .toSorted((t, e) => t - e),
              a = e[75];
            n = Math.min(
              [
                a,
                Math.floor(e.reduce((t, e) => t + e) / e.length),
                e[135],
              ].toSorted((t, e) => t - e)[r.autoTier],
              Math.floor(((r.autoCap * s.LAMPORTS_PER_SOL) / l) * 1e6)
            );
          } else n = Math.floor(((r.exact * s.LAMPORTS_PER_SOL) / l) * 1e6);
          return {
            modifyComputeUnits: c,
            modifyComputePrice: s.ComputeBudgetProgram.setComputeUnitPrice({
              microLamports: n,
            }),
          };
        };
    },
    55504: (t, e, a) => {
      "use strict";
      a.d(e, {
        Gp: () => n,
        JY: () => c,
        NR: () => u,
        O7: () => o,
        RI: () => l,
        Zj: () => r,
      });
      var s = a(79924),
        i = a(25474);
      let r = (0, s.eU)([]),
        n = (0, s.eU)([]),
        o = (0, s.eU)([]),
        u = (0, s.eU)(!1);
      (0, i.tG)("autoPrioFee", !0), (0, i.tG)("autoPrioFeeTier", 0);
      let l = (0, i.tG)("autoPrioFeeCap", 0.01),
        c = (0, i.tG)("prioritySettings", {
          auto: !0,
          autoTier: 0,
          autoCap: 0.01,
          exact: 0,
        });
    },
    69715: (t, e, a) => {
      "use strict";
      a.d(e, {
        A: () => h,
        t: () => d,
      });
      var s = a(67933),
        i = a(87606),
        r = a(16258),
        n = a(89329),
        o = a(5104),
        u = a.n(o);
      let l = async () => {
          let t = await fetch(
            "".concat("https://launcher.backwoods.gg", "/v1/auth/session"),
            {
              credentials: "include",
            }
          );
          return t.status == u().UNAUTHORIZED
            ? {
                isAuthenticated: !1,
                user: null,
              }
            : {
                isAuthenticated: !0,
                user: (await t.json()).user,
              };
        },
        c = async () => {
          await n.A.post(
            "".concat("https://launcher.backwoods.gg", "/v1/auth/signout"),
            {},
            {
              withCredentials: !0,
            }
          );
        };
      function h() {
        return (0, s.useQuery)({
          queryKey: ["auth"],
          queryFn: () => l(),
        });
      }
      function d() {
        let t = (0, i.useQueryClient)();
        return (0, r.useMutation)({
          mutationFn: c,
          onSuccess: () => {
            t.refetchQueries({
              queryKey: ["auth"],
            });
          },
        });
      }
    },
    72694: (t, e, a) => {
      "use strict";
      a.d(e, {
        CG: () => c,
        E0: () => i,
        FE: () => d,
        Gz: () => n,
        Qx: () => l,
        UU: () => r,
        dM: () => u,
        dz: () => p,
        l9: () => m,
        pH: () => h,
        zp: () => o,
      });
      var s = a(34985);
      let i =
          "https://mainnet.helius-rpc.com/?api-key=6c83432c-8d84-4a56-a73b-a0da0ebbaf48",
        r = {
          failed: "Authentication Failed",
          existing: "You must disconnect the linked account to link a new one",
          unauthorized: "You need to login first to link an account",
          duplicate: "This account is already linked to another user",
          invalid_state: "Invalid verification request. Please try again.",
          not_linked: "This Discord account is not linked to any user account",
          unknown: "Unknown Error",
        },
        n = "https://twitter.com/TheBackwoodsSol",
        o = "https://discord.gg/2zTnPkKVcz";
      new s.PublicKey("ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL");
      let u = new s.PublicKey("H5RnrnQFVYiGCsGomawwyZ1gJgmMsSXDYbpidZredcGZ"),
        l = new s.PublicKey("3hWzGFfDUUAMgdk31XVddzRU2QGLRxV7LhykdSLWtQzm"),
        c = "4GQ46io4Uh6eLbD7RLr1usYULEXCBD36ewf97s2w3vDM",
        h = "81SonfCh7n5UJE6KoFSfrpwJMzG3wR3C4i1AAzsHBNRn",
        d = "auth9SigNpDKz4sJJ1DfCTuZrZNSAgh9sFD3rboVmgg",
        p = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v";
      new s.PublicKey("81SonfCh7n5UJE6KoFSfrpwJMzG3wR3C4i1AAzsHBNRn");
      let m = "SjSpU7LqAPvA1NDfqWzJNL3cp4wwMJ1reZbMhgKwfp1";
    },
    80589: (t, e, a) => {
      "use strict";
      a.d(e, {
        $: () => o,
        s: () => n,
      });
      var s = a(60494),
        i = a(26759),
        r = a(21279),
        n = class extends i.k {
          #n;
          #o;
          #u;
          constructor(t) {
            super(),
              (this.mutationId = t.mutationId),
              (this.#o = t.mutationCache),
              (this.#n = []),
              (this.state = t.state || o()),
              this.setOptions(t.options),
              this.scheduleGc();
          }
          setOptions(t) {
            (this.options = t), this.updateGcTime(this.options.gcTime);
          }
          get meta() {
            return this.options.meta;
          }
          addObserver(t) {
            this.#n.includes(t) ||
              (this.#n.push(t),
              this.clearGcTimeout(),
              this.#o.notify({
                type: "observerAdded",
                mutation: this,
                observer: t,
              }));
          }
          removeObserver(t) {
            (this.#n = this.#n.filter((e) => e !== t)),
              this.scheduleGc(),
              this.#o.notify({
                type: "observerRemoved",
                mutation: this,
                observer: t,
              });
          }
          optionalRemove() {
            this.#n.length ||
              ("pending" === this.state.status
                ? this.scheduleGc()
                : this.#o.remove(this));
          }
          continue() {
            return this.#u?.continue() ?? this.execute(this.state.variables);
          }
          async execute(t) {
            this.#u = (0, r.II)({
              fn: () =>
                this.options.mutationFn
                  ? this.options.mutationFn(t)
                  : Promise.reject(Error("No mutationFn found")),
              onFail: (t, e) => {
                this.#l({
                  type: "failed",
                  failureCount: t,
                  error: e,
                });
              },
              onPause: () => {
                this.#l({
                  type: "pause",
                });
              },
              onContinue: () => {
                this.#l({
                  type: "continue",
                });
              },
              retry: this.options.retry ?? 0,
              retryDelay: this.options.retryDelay,
              networkMode: this.options.networkMode,
              canRun: () => this.#o.canRun(this),
            });
            let e = "pending" === this.state.status,
              a = !this.#u.canStart();
            try {
              if (!e) {
                this.#l({
                  type: "pending",
                  variables: t,
                  isPaused: a,
                }),
                  await this.#o.config.onMutate?.(t, this);
                let e = await this.options.onMutate?.(t);
                e !== this.state.context &&
                  this.#l({
                    type: "pending",
                    context: e,
                    variables: t,
                    isPaused: a,
                  });
              }
              let s = await this.#u.start();
              return (
                await this.#o.config.onSuccess?.(
                  s,
                  t,
                  this.state.context,
                  this
                ),
                await this.options.onSuccess?.(s, t, this.state.context),
                await this.#o.config.onSettled?.(
                  s,
                  null,
                  this.state.variables,
                  this.state.context,
                  this
                ),
                await this.options.onSettled?.(s, null, t, this.state.context),
                this.#l({
                  type: "success",
                  data: s,
                }),
                s
              );
            } catch (e) {
              try {
                throw (
                  (await this.#o.config.onError?.(
                    e,
                    t,
                    this.state.context,
                    this
                  ),
                  await this.options.onError?.(e, t, this.state.context),
                  await this.#o.config.onSettled?.(
                    void 0,
                    e,
                    this.state.variables,
                    this.state.context,
                    this
                  ),
                  await this.options.onSettled?.(
                    void 0,
                    e,
                    t,
                    this.state.context
                  ),
                  e)
                );
              } finally {
                this.#l({
                  type: "error",
                  error: e,
                });
              }
            } finally {
              this.#o.runNext(this);
            }
          }
          #l(t) {
            (this.state = ((e) => {
              switch (t.type) {
                case "failed":
                  return {
                    ...e,
                    failureCount: t.failureCount,
                    failureReason: t.error,
                  };
                case "pause":
                  return {
                    ...e,
                    isPaused: !0,
                  };
                case "continue":
                  return {
                    ...e,
                    isPaused: !1,
                  };
                case "pending":
                  return {
                    ...e,
                    context: t.context,
                    data: void 0,
                    failureCount: 0,
                    failureReason: null,
                    error: null,
                    isPaused: t.isPaused,
                    status: "pending",
                    variables: t.variables,
                    submittedAt: Date.now(),
                  };
                case "success":
                  return {
                    ...e,
                    data: t.data,
                    failureCount: 0,
                    failureReason: null,
                    error: null,
                    status: "success",
                    isPaused: !1,
                  };
                case "error":
                  return {
                    ...e,
                    data: void 0,
                    error: t.error,
                    failureCount: e.failureCount + 1,
                    failureReason: t.error,
                    isPaused: !1,
                    status: "error",
                  };
              }
            })(this.state)),
              s.j.batch(() => {
                this.#n.forEach((e) => {
                  e.onMutationUpdate(t);
                }),
                  this.#o.notify({
                    mutation: this,
                    type: "updated",
                    action: t,
                  });
              });
          }
        };
      function o() {
        return {
          context: void 0,
          data: void 0,
          error: null,
          failureCount: 0,
          failureReason: null,
          isPaused: !1,
          status: "idle",
          variables: void 0,
          submittedAt: 0,
        };
      }
    },
    93051: (t, e, a) => {
      "use strict";
      a.d(e, {
        n: () => r,
        v: () => n,
      });
      var s = a(34985),
        i = a(50887).Buffer;
      let r = (t, e, a) => {
          let [r] = s.PublicKey.findProgramAddressSync(
              [i.from("pool"), t.toBuffer(), e.toBuffer()],
              a.programId
            ),
            [n] = s.PublicKey.findProgramAddressSync(
              [i.from("vault"), r.toBuffer()],
              a.programId
            );
          return {
            pool: r,
            vault: n,
          };
        },
        n = (t, e, a, r) => {
          let [n] = s.PublicKey.findProgramAddressSync(
              [i.from("staking_pool"), t.toBuffer(), a.toBuffer()],
              r.programId
            ),
            [o] = s.PublicKey.findProgramAddressSync(
              [i.from("staking_vault"), n.toBuffer()],
              r.programId
            ),
            [u] = s.PublicKey.findProgramAddressSync(
              [i.from("reward_vault"), n.toBuffer()],
              r.programId
            );
          return {
            stakingPool: n,
            stakingVault: o,
            rewardVault: u,
          };
        };
    },
  },
  (t) => {
    var e = (e) => t((t.s = e));
    t.O(
      0,
      [
        1426, 8033, 693, 7508, 887, 607, 9329, 7933, 7261, 3237, 3970, 5104,
        2432, 2824, 5474, 2430, 8615, 587, 8315, 7358,
      ],
      () => e(1185)
    ),
      (_N_E = t.O());
  },
]);
