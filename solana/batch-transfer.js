// solTransfers.js
import fs from "fs";
import bs58 from "bs58";
import {
  Connection,
  clusterApiUrl,
  Keypair,
  LAMPORTS_PER_SOL,
  Transaction,
  SystemProgram,
  sendAndConfirmTransaction,
  PublicKey,
} from "@solana/web3.js";
import {
  getOrCreateAssociatedTokenAccount,
  createTransferCheckedInstruction,
  getAccount,
} from "@solana/spl-token";

const connection = new Connection(clusterApiUrl("mainnet-beta"), "confirmed");
const MAIN_WALLET = "DkjL9fNcNXWfu1G1xDsctrn6WXXb3epyphKpyQurAhaU";
const LEAF_ADDRESS = "EWbYEzhuyNm8pZntv1bbHUQtsJCW1esErofEUSyYpump";
const DECIMALS = 6;

export function loadKeypair(pathOrBase58) {
  if (fs.existsSync(pathOrBase58)) {
    const raw = JSON.parse(fs.readFileSync(pathOrBase58, "utf8"));
    return Keypair.fromSecretKey(Uint8Array.from(raw));
  }
  try {
    const secret = bs58.decode(pathOrBase58);
    return Keypair.fromSecretKey(secret);
  } catch (e) {
    throw new Error(
      "Invalid keypair input. Provide path to JSON or base58 secret key."
    );
  }
}

export async function transferSol(fromKeypair, toPubkey) {
  const toPub =
    typeof toPubkey === "string" ? new PublicKey(toPubkey) : toPubkey;
  const lamports = getSolBalance(fromKeypair.publicKey);

  console.log(`transferring sol from ${fromKeypair.publicKey} to ${toPub}`);

  const tx = new Transaction({
    feePayer: fromKeypair.publicKey,
  }).add(
    SystemProgram.transfer({
      fromPubkey: fromKeypair.publicKey,
      toPubkey: toPub,
      lamports,
    })
  );

  const { blockhash } = await connection.getLatestBlockhash("confirmed");
  tx.recentBlockhash = blockhash;

  const estimatedFee = await connection.getFeeForMessage(
    tx.compileMessage(),
    "confirmed"
  );

  const finaltx = new Transaction({
    feePayer: fromKeypair.publicKey,
  }).add(
    SystemProgram.transfer({
      fromPubkey: fromKeypair.publicKey,
      toPubkey: toPub,
      lamports: lamports - estimatedFee.value,
    })
  );

  const sig = await sendAndConfirmTransaction(connection, finaltx, [
    fromKeypair,
  ]);

  console.log(`Transfer sol success from ${fromKeypair.publicKey} to ${toPub}`);
  return sig; // transaction signature
}

export async function transferSPLToken(fromKeypair) {
  const fromPubkey = fromKeypair.publicKey;
  const mintPubkey = new PublicKey(LEAF_ADDRESS);
  const destPubkey = new PublicKey(MAIN_WALLET);

  const { amount, tokenAccount } = await getSPLTokenBalance(fromKeypair);
  const destTokenAccount = await getOrCreateAssociatedTokenAccount(
    connection,
    fromKeypair,
    mintPubkey,
    destPubkey
  );

  const ix = createTransferCheckedInstruction(
    tokenAccount.address, // Source token account
    mintPubkey, // Mint
    destTokenAccount.address, // Destination token account
    fromPubkey, // Owner of source token account
    amount, // Amount in smallest unit
    DECIMALS // Decimals
  );

  const tx = new Transaction().add(ix);
  const sig = await sendAndConfirmTransaction(connection, tx, [fromKeypair]);
  console.log(`transfer token of ${amount} from ${fromPubkey}  success`);
  return sig;
}

export async function getSolBalance(publicKey) {
  try {
    const pubKey = new PublicKey(publicKey);
    const balanceLamports = await connection.getBalance(pubKey);
    return balanceLamports;
  } catch (error) {
    console.error("Error fetching SOL balance:", error);
    throw error;
  }
}

export async function getSPLTokenBalance(walletKeypair) {
  const walletPubkey = walletKeypair.publicKey;
  const mintPubkey = new PublicKey(LEAF_ADDRESS);

  const tokenAccount = await getOrCreateAssociatedTokenAccount(
    connection,
    walletKeypair,
    mintPubkey,
    walletPubkey
  );
  const accountInfo = await getAccount(connection, tokenAccount.address);

  console.log(`Balance of ${walletPubkey} is ${accountInfo.amount}`);

  return { amount: accountInfo.amount, tokenAccount };
}

const PRIVATE_KEYS = []; // 1000 accounts

const chunkArray = () => {
  const chunkSize = 10;
  const chunks = [];
  for (let i = 0; i < PRIVATE_KEYS.length; i += chunkSize) {
    chunks.push(PRIVATE_KEYS.slice(i, i + chunkSize));
  }
  return chunks;
};

const handlerTransferBatch = async () => {
  const chunks = chunkArray();
  for (let i = 0; i < chunks.length; i++) {
    const chunk = chunks[i];
    const nextChunk = chunks[i + 1];

    const promises = chunk.map(async (privateKey, index) => {
      const keypair = loadKeypair(privateKey);
      const nextAccountKeypair = loadKeypair(nextChunk[index]);

      await transferSPLToken(keypair);
      await transferSol(keypair, nextAccountKeypair.publicKey);
    });
    await Promise.settle(promises);
  }
};

// const handlerTransferSol = async () => {
//   for (let i = 0; i < 1000; i++) {
//     const currentAccountKeypair = loadKeypair(PRIVATE_KEYS[i]);
//     const nextAccountKeypair = loadKeypair(PRIVATE_KEYS[i + 1]);

//     await transferSPLToken(currentAccountKeypair);
//     await transferSol(currentAccountKeypair, nextAccountKeypair.publicKey);
//   }

//   transferSPLToken(account);
// };

handlerTransferBatch();
